/**
 * REGULAR FIELD COMPONENT
 * 
 * This component handles regular form fields in dynamic forms, including text inputs,
 * dropdowns, and other field types. It integrates with the unified dropdown component
 * for dropdown fields and manages form control state.
 * 
 * PURPOSE:
 * - Renders different types of form fields based on field configuration
 * - Integrates with unified dropdown component for dropdown fields
 * - Manages form control state (enabled/disabled)
 * - Handles field value changes and validation
 * - Supports multi-field arrays and nested forms
 * 
 * FEATURES:
 * - Dynamic field rendering based on field type
 * - Dropdown integration for foreign key fields
 * - Form control state management
 * - Multi-field support
 * - View mode support (read-only)
 * - Field validation integration
 * 
 * FIELD TYPES:
 * - Text inputs (regular text fields)
 * - Dropdown fields (type, foreignKey, regular)
 * - Multi-field arrays
 * - Nested form groups
 * 
 * USAGE:
 * <app-regular-field 
 *   [field]="field"
 *   [form]="form"
 *   [isViewMode]="false"
 *   [fields]="fields"
 *   [multiIndex]="1"
 *   (fieldValueChange)="onFieldValueChange($event)">
 * </app-regular-field>
 */

import { Component, Input, Output, EventEmitter, OnDestroy, OnInit, OnChanges, inject, ChangeDetectorRef } from '@angular/core';
import { FormGroup, FormArray } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { DropdownComponent, DropdownConfig, DropdownValueChangeEvent } from '../dropdown/dropdown.component';

/**
 * Regular Field Component
 * 
 * Provides dynamic field rendering for dynamic forms with the following capabilities:
 * - Dynamic field type rendering based on configuration
 * - Integration with unified dropdown component
 * - Form control state management
 * - Multi-field array support
 * - View mode support
 * - Field validation integration
 * 
 * FIELD RENDERING:
 * - Text inputs for regular fields
 * - Dropdowns for foreign key fields
 * - Multi-field arrays with proper indexing
 * - Nested form groups for complex data
 * 
 * DROPDOWN INTEGRATION:
 * - Type dropdowns for field type selection
 * - Foreign key dropdowns for relationship fields
 * - Regular dropdowns for enumerated values
 * - Unified dropdown component for consistency
 * 
 * STATE MANAGEMENT:
 * - Form control enabled/disabled state
 * - View mode state handling
 * - Field value change tracking
 * - Validation state management
 */
@Component({
  selector: 'app-regular-field',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatIconModule,
    MatTooltipModule,
    DropdownComponent
  ],
  templateUrl: './regular-field.component.html',
  styleUrl: './regular-field.component.scss'
})
export class RegularFieldComponent implements OnInit, OnDestroy, OnChanges {
  
  // ==================== CORE INPUTS ====================
  
  /** Field configuration object defining field properties and behavior */
  @Input() field!: any;
  
  /** Angular FormGroup for reactive form integration */
  @Input() form!: FormGroup;
  
  /** Whether the form is in view mode (read-only) */
  @Input() isViewMode: boolean = false;
  
  /** Array of all field definitions for context and reference */
  @Input() fields: any[] = [];
  
  /** Optional index for multi-fields (1-based indexing) */
  @Input() multiIndex?: number;

  // ==================== OUTPUT EVENTS ====================
  
  /** Emitted when field value changes */
  @Output() fieldValueChange = new EventEmitter<{fieldName: string, value: any}>();

  // ==================== DEPENDENCY INJECTION ====================
  
  /** Change detector reference for manual change detection */
  private cdr = inject(ChangeDetectorRef);

  /**
   * LIFECYCLE: Component Initialization
   * 
   * Sets up the component and ensures proper form control state.
   * Note: Dropdown preloading is handled by the unified DropdownComponent.
   */
  ngOnInit() {
    // Dropdown preloading is now handled by the unified DropdownComponent
    // Ensure form control is properly disabled when it should be
    this.updateFormControlDisabledState();
  }

  /**
   * LIFECYCLE: Input Changes Detection
   * 
   * Responds to changes in component inputs and updates internal state accordingly.
   */
  ngOnChanges(): void {
    // Update form control disabled state when inputs change
    this.updateFormControlDisabledState();
  }

  /**
   * LIFECYCLE: Component Cleanup
   * 
   * Performs cleanup operations when component is destroyed.
   * Note: Cleanup is primarily handled by the unified DropdownComponent.
   */
  ngOnDestroy() {
    // Cleanup handled by unified DropdownComponent
  }

  // ==================== FORM CONTROL MANAGEMENT ====================
  
  /**
   * Updates the disabled state of the form control based on component inputs
   * Ensures form control state matches component disabled/readonly state
   */
  private updateFormControlDisabledState(): void {
    const formControl = this.form.get(this.field.fieldName);
    if (formControl) {
      if (this.isViewMode || this.field.noInput) {
        if (formControl.enabled) {
          formControl.disable();
        }
      } else {
        if (formControl.disabled) {
          formControl.enable();
        }
      }
    }
  }

  // ==================== DROPDOWN CONFIGURATION ====================
  
  /**
   * Creates configuration for type dropdown fields
   * Configures dropdown for field type selection
   * 
   * @param field - Field configuration object
   * @returns DropdownConfig object for type dropdown
   */
  getTypeDropdownConfig(field: any): DropdownConfig {
    return {
      type: 'type',  // Type dropdown type
      queryBuilderId: 'fieldType',  // Query builder for field types
      searchEnabled: true,  // Enable search functionality
      placeholder: `Search ${field.label?.trim() || field.fieldName}`,  // User-friendly placeholder
      emptyMessage: 'No types found',  // Message when no options available
      tooltip: 'Show type suggestions'  // Tooltip for accessibility
    };
  }

  /**
   * Creates configuration for foreign key dropdown fields
   * Configures dropdown for foreign key selection
   * 
   * @param field - Field configuration object
   * @returns DropdownConfig object for foreign key dropdown
   */
  getForeignKeyDropdownConfig(field: any): DropdownConfig {
    return {
      type: 'foreignKey',  // Foreign key dropdown type
      queryBuilderId: 'formDefinition',  // Query builder for form definitions
      searchEnabled: true,  // Enable search functionality
      placeholder: `Search ${field.label?.trim() || field.fieldName}`,  // User-friendly placeholder
      emptyMessage: 'No foreign keys found',  // Message when no options available
      tooltip: 'Show foreign key suggestions'  // Tooltip for accessibility
    };
  }

  /**
   * Creates configuration for regular dropdown fields
   * Configures dropdown for regular option selection
   * 
   * @param field - Field configuration object
   * @returns DropdownConfig object for regular dropdown
   */
  getRegularDropdownConfig(field: any): DropdownConfig {
    return {
      type: 'regular',  // Regular dropdown type
      queryBuilderId: field.foreginKey,  // Use field's foreign key as query builder
      searchEnabled: true,  // Enable search functionality
      placeholder: `Search ${field.label?.trim() || field.fieldName}`,  // User-friendly placeholder
      emptyMessage: 'No options found',  // Message when no options available
      tooltip: 'Show options'  // Tooltip for accessibility
    };
  }

  // ==================== EVENT HANDLERS ====================
  
  /**
   * Handles value changes from the unified dropdown component
   * Emits field value change event for parent component handling
   * 
   * @param event - Dropdown value change event
   */
  onDropdownValueChange(event: DropdownValueChangeEvent): void {
    // Emit the field value change event for parent component
    this.fieldValueChange.emit({
      fieldName: event.fieldName,
      value: event.value
    });
  }

  // ==================== FORM CONTROL HELPERS ====================
  
  /**
   * Helper method to get FormControl with proper typing
   * Provides type-safe access to form controls
   * 
   * @param fieldName - Name of the form field
   * @returns FormControl for the specified field
   */
  getFormControl(fieldName: string): any {
    return this.form.get(fieldName);
  }
}
