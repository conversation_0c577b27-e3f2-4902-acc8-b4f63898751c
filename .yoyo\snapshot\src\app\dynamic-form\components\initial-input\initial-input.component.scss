/**
 * INITIAL INPUT COMPONENT STYLES
 * 
 * This SCSS file provides comprehensive styling for the initial input component
 * with support for responsive design, button themes, and form integration.
 * 
 * DESIGN SYSTEM:
 * - Uses Poppins font family for consistency
 * - Material Design color palette for buttons
 * - Consistent spacing and border radius
 * - Smooth transitions for better UX
 * 
 * LAYOUT STRUCTURE:
 * - Form container with flexbox layout
 * - Horizontal alignment for desktop
 * - Stacked layout for mobile devices
 * - Proper spacing and padding throughout
 * 
 * BUTTON THEMES:
 * - Add: Green (#4CAF50) for creation actions
 * - Edit: Blue (#2196F3) for modification actions
 * - View: Orange (#FF9800) for viewing actions
 * - Maintenance: Red (#F44336) for maintenance actions
 * 
 * RESPONSIVE DESIGN:
 * - Desktop-first approach with mobile breakpoints
 * - Breakpoints at 1024px, 768px, 480px, and 360px
 * - Flexible layouts that adapt to screen size
 * - Touch-friendly button sizes on mobile
 */

@use '../../../../styles/shared.scss' as *;

/* ==================== FORM CONTAINER ==================== */

/**
 * FORM CONTAINER
 * 
 * Main wrapper for the initial input form:
 * - Flexbox column layout for vertical stacking
 * - Proper gap spacing between elements
 * - Full width utilization
 * - White background for clean appearance
 */
.form-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
  width: 100%;
  background-color: white;
}

/* ==================== MAIN FIELD SECTION ==================== */

/**
 * FORM MAIN FIELD
 * 
 * Container for ID input and action buttons:
 * - Horizontal flexbox layout for desktop
 * - Proper alignment and spacing
 * - Responsive padding and gap
 * - Clean white background
 */
.form-main-field {
  display: flex;
  align-items: center;
  gap: 24px;
  width: 100%;
  padding-top: 16px;
  background-color: white;
}

/**
 * FORM LABEL
 * 
 * Styling for the ID field label:
 * - Fixed width for consistent alignment
 * - Poppins font with medium weight
 * - Proper font size and color
 * - Left padding for visual balance
 */
.form-label {
  width: 160px;
  font-family: 'Poppins', sans-serif;
  font-weight: 500;
  font-size: 18px;
  color: #000000;
  padding-left: 24px;
}

/* ==================== INPUT AND BUTTON GROUP ==================== */

/**
 * INPUT BUTTON GROUP
 * 
 * Container for ID input and action buttons:
 * - Flexbox layout with proper alignment
 * - Responsive gap spacing
 * - Full width utilization with flex-grow
 * - Right padding for visual balance
 * - Wrap behavior for responsive design
 */
.input-button-group {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-grow: 1;
  width: 100%;
  padding-right: 24px;
  flex-wrap: wrap;
  min-width: 0;
}

/* ==================== FORM INPUT STYLING ==================== */

/**
 * FORM INPUT BASE STYLES
 * 
 * Consistent styling for form inputs:
 * - Flex-grow for responsive width
 * - 40px height for touch-friendly interaction
 * - Rounded corners with 8px border radius
 * - Poppins font for modern typography
 * - Smooth transitions for state changes
 * - Proper padding and box sizing
 */
.form-input {
  flex-grow: 1;
  height: 40px;
  border: 1px solid #DBDBDB;
  border-radius: 8px;
  font-family: 'Poppins', sans-serif;
  font-size: 16px;
  color: #222222;
  background: #FFFFFF;
  transition: all 0.3s ease;
  padding: 0 12px;
  
  /**
   * FOCUS STATE
   * 
   * Enhanced visual feedback when input is focused:
   * - Gray border color for subtle indication
   * - Box shadow for depth and emphasis
   * - Maintains accessibility standards
   */
  &:focus {
    outline: none;
    border-color: #9e9e9e;
    box-shadow: 0 0 0 2px rgba(158, 158, 158, 0.25);
  }
  
  /**
   * INVALID INPUT STATE
   * 
   * Error state styling for validation feedback:
   * - Red border color for clear error indication
   * - Light red background for subtle emphasis
   * - Enhanced focus state with red shadow
   */
  &.invalid-input {
    border-color: #f44336;
    background-color: rgba(244, 67, 54, 0.05);
    
    &:focus {
      box-shadow: 0 0 0 2px rgba(244, 67, 54, 0.2);
    }
  }
  
  /**
   * PLACEHOLDER STYLING
   * 
   * Subtle placeholder text styling:
   * - Light gray color for reduced emphasis
   * - Maintains readability while being unobtrusive
   */
  &::placeholder {
    color: #999;
  }
}

/* ==================== ID INPUT CONTAINER ==================== */

/**
 * ID INPUT CONTAINER
 * 
 * Wrapper for the unified dropdown component:
 * - Relative positioning for proper layout
 * - Flexbox alignment for responsive behavior
 * - Full width utilization with flex-grow
 * - Minimum width constraints for usability
 * - Ensures dropdown component takes full width
 */
.id-input-container {
  position: relative;
  display: flex;
  align-items: center;
  flex-grow: 1;
  width: 100%;
  min-width: 0;
  flex: 1;
}

/**
 * DROPDOWN COMPONENT STYLING
 * 
 * Ensures dropdown component takes full width in initial-input context:
 * - Full width utilization
 * - Block display for proper layout
 * - Consistent behavior across contexts
 */
.id-input-container app-dropdown {
  width: 100%;
  display: block;
}

.id-input-container app-dropdown ::ng-deep .dropdown-input-container {
  width: 100%;
  min-width: 100%;
}

/* ==================== RESPONSIVE DROPDOWN STYLES ==================== */

/**
 * TABLET RESPONSIVE STYLING (max-width: 768px)
 * 
 * Adjustments for tablet-sized screens:
 * - Reduced minimum width for better fit
 * - Smaller font size for space efficiency
 * - Reduced padding for compact layout
 * - Ensures buttons wrap properly
 */
@media (max-width: 768px) {
  .id-input-container app-dropdown ::ng-deep .dropdown-input-container {
    min-width: 0;
    width: 100%;
  }
  
  .id-input-container app-dropdown ::ng-deep .form-input {
    font-size: 14px;
    padding: 0 8px;
  }
  
  /* Ensure buttons wrap properly */
  .input-button-group {
    flex-wrap: wrap;
    justify-content: center;
  }
}

/**
 * MOBILE RESPONSIVE STYLING (max-width: 480px)
 * 
 * Adjustments for mobile-sized screens:
 * - Further reduced font size and padding
 * - Smaller input height for mobile optimization
 * - Reduced button sizes for touch interaction
 */
@media (max-width: 480px) {
  .id-input-container app-dropdown ::ng-deep .form-input {
    font-size: 13px;
    padding: 0 6px;
    height: 36px;
  }
  
  .id-input-container app-dropdown ::ng-deep .dropdown-arrow-btn {
    width: 36px;
    height: 36px;
  }
}

/**
 * SMALL MOBILE RESPONSIVE STYLING (max-width: 360px)
 * 
 * Adjustments for very small mobile screens:
 * - Minimal font size and padding
 * - Compact input height
 * - Smallest button sizes for space efficiency
 */
@media (max-width: 360px) {
  .id-input-container app-dropdown ::ng-deep .form-input {
    font-size: 12px;
    padding: 0 4px;
    height: 32px;
  }
  
  .id-input-container app-dropdown ::ng-deep .dropdown-arrow-btn {
    width: 32px;
    height: 32px;
  }
}

/* ==================== BUTTON CONTAINER ==================== */

/**
 * BUTTON CONTAINER
 * 
 * Groups action buttons with rounded corners:
 * - Flexbox layout for button alignment
 * - Light background with subtle shadow
 * - Rounded corners for modern appearance
 * - Proper spacing and responsive behavior
 * - Border for visual definition
 */
.button-container {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background-color: #f8f9fa;
  border-radius: 12px;
  border: 1px solid #e9ecef;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

/* ==================== INITIAL INPUT BUTTON STYLES ==================== */

/**
 * ENHANCED INITIAL INPUT BUTTON STYLES
 * 
 * Base styling for all action buttons:
 * - 36px square for touch-friendly interaction
 * - Rounded corners with 8px border radius
 * - Poppins font for consistency
 * - Smooth transitions for hover effects
 * - Proper positioning and overflow handling
 * - Box shadow for depth and emphasis
 * - Flex-shrink prevention for consistent sizing
 */
.initial-input-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border: 2px solid;
  border-radius: 8px;
  font-family: 'Poppins', sans-serif;
  cursor: pointer;
  transition: all 0.3s ease;
  padding: 0;
  margin: 0;
  min-width: 36px;
  min-height: 36px;
  position: relative;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  flex-shrink: 0;
  
  /**
   * BUTTON ICON STYLING
   * 
   * Material Design icon styling:
   * - 16px size for optimal visibility
   * - Absolute positioning for perfect centering
   * - Transform for precise alignment
   * - Proper line height and spacing
   */
  mat-icon {
    font-size: 16px;
    width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    line-height: 1;
    margin: 0;
    padding: 0;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
}

/* ==================== BUTTON THEMES ==================== */

/**
 * ADD BUTTON - GREEN THEME
 * 
 * Styling for the add button:
 * - Green border and icon color
 * - White background for contrast
 * - Hover effects with color inversion
 * - Subtle transform and shadow effects
 * - Used for creating new records
 */
.initial-input-button.add-button {
  border-color: #4CAF50;
  background-color: white;
  color: #4CAF50;
  
  &:hover {
    background-color: #4CAF50;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(76, 175, 80, 0.3);
  }
}

/**
 * EDIT BUTTON - BLUE THEME
 * 
 * Styling for the edit button:
 * - Blue border and icon color
 * - White background for contrast
 * - Hover effects with color inversion
 * - Consistent styling with other buttons
 * - Used for modifying existing records
 */
.initial-input-button.edit-button {
  border-color: #2196F3;
  background-color: white;
  color: #2196F3;
  
  &:hover {
    background-color: #2196F3;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(33, 150, 243, 0.3);
  }
}

/**
 * VIEW BUTTON - ORANGE THEME
 * 
 * Styling for the view button:
 * - Orange border and icon color
 * - White background for contrast
 * - Hover effects with color inversion
 * - Distinct color for clear action identification
 * - Used for viewing record details
 */
.initial-input-button.view-button {
  border-color: #FF9800;
  background-color: white;
  color: #FF9800;
  
  &:hover {
    background-color: #FF9800;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(255, 152, 0, 0.3);
  }
}

/**
 * MAINTENANCE BUTTON - RED THEME
 * 
 * Styling for the maintenance button:
 * - Red border and icon color
 * - White background for contrast
 * - Hover effects with color inversion
 * - Warning color for maintenance actions
 * - Used for maintenance operations
 */
.initial-input-button.maintenance-button {
  border-color: #F44336;
  background-color: white;
  color: #F44336;
  
  &:hover {
    background-color: #F44336;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(244, 67, 54, 0.3);
  }
}

/* ==================== RESPONSIVE DESIGN ==================== */

/**
 * LARGE TABLET RESPONSIVE STYLING (max-width: 1024px)
 * 
 * Adjustments for large tablet screens:
 * - Reduced gaps and padding for space efficiency
 * - Smaller label width and font size
 * - Compact button container styling
 * - Maintained usability and touch interaction
 */
@media (max-width: 1024px) {
  .form-main-field {
    gap: 16px;
    padding: 16px 12px;
  }
  
  .form-label {
    width: 120px;
    font-size: 16px;
    padding-left: 12px;
  }
  
  .input-button-group {
    padding-right: 12px;
    gap: 8px;
    flex-wrap: wrap;
  }
  
  .id-input-container {
    min-width: 200px;
    flex: 1;
  }
  
  .button-container {
    padding: 6px 10px;
    gap: 6px;
  }
  
  .initial-input-button {
    width: 32px;
    height: 32px;
    min-width: 32px;
    min-height: 32px;
    
    mat-icon {
      font-size: 14px;
      width: 14px;
      height: 14px;
      line-height: 1;
      margin: 0;
      padding: 0;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }
  }
}

/**
 * TABLET RESPONSIVE STYLING (max-width: 768px)
 * 
 * Adjustments for tablet-sized screens:
 * - Stacked layout for better mobile experience
 * - Centered alignment for visual balance
 * - Reduced spacing and padding
 * - Maintained touch-friendly button sizes
 */
@media (max-width: 768px) {
  .form-main-field {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
    padding: 12px 8px;
  }
  
  .form-label {
    width: 100%;
    padding-left: 0;
    text-align: center;
    font-size: 16px;
  }
  
  .input-button-group {
    padding-right: 0;
    justify-content: center;
    flex-wrap: wrap;
    width: 100%;
    gap: 6px;
  }
  
  .id-input-container {
    width: 100%;
    min-width: 0;
    flex: 1;
    margin-bottom: 8px;
  }
  
  .button-container {
    padding: 6px 8px;
    gap: 4px;
    justify-content: center;
  }
  
  .initial-input-button {
    width: 32px;
    height: 32px;
    min-width: 32px;
    min-height: 32px;
    flex-shrink: 0;
    
    mat-icon {
      font-size: 14px;
      width: 14px;
      height: 14px;
      line-height: 1;
      margin: 0;
      padding: 0;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }
  }
}

/**
 * MOBILE RESPONSIVE STYLING (max-width: 480px)
 * 
 * Adjustments for mobile-sized screens:
 * - Minimal padding and spacing
 * - Compact layout for small screens
 * - Reduced button sizes for space efficiency
 * - Maintained usability on small devices
 */
@media (max-width: 480px) {
  .form-main-field {
    padding: 8px 4px;
    gap: 8px;
  }
  
  .form-label {
    font-size: 14px;
    padding: 4px 0;
  }
  
  .input-button-group {
    gap: 4px;
    padding: 0 4px;
    justify-content: space-between;
  }
  
  .id-input-container {
    min-width: 0;
    flex: 1;
    margin-bottom: 6px;
  }
  
  .button-container {
    padding: 4px 6px;
    gap: 3px;
  }
  
  .initial-input-button {
    width: 28px;
    height: 28px;
    min-width: 28px;
    min-height: 28px;
    
    mat-icon {
      font-size: 12px;
      width: 12px;
      height: 12px;
      line-height: 1;
      margin: 0;
      padding: 0;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }
  }
}

/**
 * SMALL MOBILE RESPONSIVE STYLING (max-width: 360px)
 * 
 * Adjustments for very small mobile screens:
 * - Minimal spacing and padding
 * - Smallest button sizes for space efficiency
 * - Compact layout for tiny screens
 * - Maintained functionality on very small devices
 */
@media (max-width: 360px) {
  .form-main-field {
    padding: 6px 2px;
  }
  
  .form-label {
    font-size: 13px;
  }
  
  .input-button-group {
    gap: 3px;
    padding: 0 2px;
    justify-content: space-between;
  }
  
  .id-input-container {
    margin-bottom: 4px;
  }
  
  .button-container {
    padding: 3px 4px;
    gap: 2px;
  }
  
  .initial-input-button {
    width: 24px;
    height: 24px;
    min-width: 24px;
    min-height: 24px;
    
    mat-icon {
      font-size: 10px;
      width: 10px;
      height: 10px;
      line-height: 1;
      margin: 0;
      padding: 0;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }
  }
}
