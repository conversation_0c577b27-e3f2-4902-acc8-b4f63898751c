@if (!submissionSuccess) {
  <div class="form-container">
    <!-- Initial Input Section -->
    @if (showInitialInput) {
      <div class="initial-input">
        <form [formGroup]="form">
          @if (errorMessage) {
            <div class="error-message">
              {{ errorMessage }}
            </div>
          }

          <div class="form-field">
            <label for="ID">ID</label>
            <input formControlName="ID" id="ID" type="text" required>

            <div class="button-group">
              <button type="button" (click)="loadDataAndBuildForm()" [disabled]="isLoading" class="action-button">
                @if (isLoading) {
                  <span>Loading...</span>
                } @else {
                  <span>Load Data</span>
                }
              </button>

              <button type="button" (click)="viewData()" [disabled]="isLoading" class="action-button">
                @if (isLoading) {
                  <span>Loading...</span>
                } @else {
                  <span>View Data</span>
                }
              </button>
            </div>
          </div>
        </form>
      </div>
    }

    <!-- Main Form Section -->
    @if (!showInitialInput) {
      <div class="form-grid">
    <form [formGroup]="form" (ngSubmit)="onSubmit()">
      <!-- Button Group at the Top -->
      <div class="button-group">
        <button type="button" (click)="goBack()" class="action-button">Back</button>
        <button type="button" (click)="authorizeRecord()" class="action-button">Auth</button>
        <button type="submit" [disabled]="form.invalid || isViewMode" class="action-button">Submit</button>
      </div>

      <!-- ID Display -->
      <div class="form-field">
        <label>ID</label>
        <p>{{ form.get('ID')?.value }}</p>
      </div>

      <!-- Dynamic Form Fields -->
      @for (field of fields; track field.fieldName) {
        <!-- Multi-field (Array) handling -->
        @if (field.isMulti) {
          <div [formArrayName]="field.fieldName" class="field-group">
            <h3>{{ field.fieldName }}</h3>
            @for (control of getMultiArray(field.fieldName).controls; track $index) {
              <div [formGroupName]="$index" class="form-grid multi-field">
                <label [for]="field.fieldName">{{ field.fieldName }} ({{ $index + 1 }})</label>
                <input
                  [formControlName]="field.fieldName"
                  [id]="field.fieldName + '-' + $index"
                  type="text"
                  [required]="field.mandatory"
                  [readonly]="isViewMode">

                @if (getMultiArray(field.fieldName).controls.length > 1 && !isViewMode) {
                  <button type="button" (click)="removeMultiField(field.fieldName, $index)" class="remove-button">
                    <i class="fas fa-trash-alt"></i>
                  </button>
                }

                @if (!isViewMode) {
                  <button type="button" (click)="addMultiField(field, $index)" class="add-button">
                    <i class="fas fa-plus-circle"></i>
                  </button>
                }
              </div>
            }
          </div>
        }

        <!-- Single Field (Non-array) -->
        @if (!field.isMulti && !field.Group && field.fieldName !== 'ID') {
          <div class="form-field">
            <label [for]="field.fieldName">{{ field.fieldName }}</label>

            @if (field.type === 'boolean') {
              <input
                [formControlName]="field.fieldName"
                [id]="field.fieldName"
                type="checkbox"
                [required]="field.mandatory"
                [readonly]="isViewMode">
            }

            @if (field.type === 'string') {
              <input
                [formControlName]="field.fieldName"
                [id]="field.fieldName"
                type="text"
                [required]="field.mandatory"
                [readonly]="isViewMode">
            }

            @if (field.type === 'int') {
              <input
                [formControlName]="field.fieldName"
                [id]="field.fieldName"
                type="number"
                [required]="field.mandatory"
                [readonly]="isViewMode">
            }

            @if (form.get(field.fieldName)?.invalid && form.get(field.fieldName)?.touched) {
              <div>
                <small class="error">This field is required</small>
              </div>
            }
          </div>
        }

        <!-- Group Fields -->
        @if (field.Group && isFirstFieldInGroup(field)) {
          <div [formArrayName]="field.Group" class="field-group">
            <h3>{{ field.Group }}</h3>
            @for (group of getGroupArray(field.Group).controls; track $index) {
              <div [formGroupName]="$index" class="form-grid multi-field">
                <div class="group-fields">
                  @for (groupField of getFieldsForGroup(field.Group); track groupField.fieldName) {
                    <div class="form-field">
                      <label [for]="groupField.fieldName">{{ groupField.fieldName }}</label>

                      @if (groupField.type === 'string') {
                        <input
                          [formControlName]="groupField.fieldName"
                          [id]="groupField.fieldName"
                          type="text"
                          [required]="groupField.mandatory"
                          [readonly]="isViewMode">
                      }

                      @if (groupField.type === 'int') {
                        <input
                          [formControlName]="groupField.fieldName"
                          [id]="groupField.fieldName"
                          type="number"
                          [required]="groupField.mandatory"
                          [readonly]="isViewMode">
                      }

                      @if (groupField.type === 'boolean') {
                        <input
                          [formControlName]="groupField.fieldName"
                          [id]="groupField.fieldName"
                          type="checkbox"
                          [required]="groupField.mandatory"
                          [readonly]="isViewMode">
                      }

                      @if (getGroupArray(field.Group).at($index).get(groupField.fieldName)?.invalid && getGroupArray(field.Group).at($index).get(groupField.fieldName)?.touched) {
                        <div>
                          <small class="error">This field is required</small>
                        </div>
                      }
                    </div>
                  }

                  @if (getGroupArray(field.Group).controls.length > 1 && !isViewMode) {
                    <button type="button" (click)="removeGroup(field.Group, $index)" class="remove-button">
                      <i class="fas fa-trash-alt"></i>
                    </button>
                  }

                  @if (!isViewMode) {
                    <button type="button" (click)="addGroup(field.Group, $index)" class="add-button">
                      <i class="fas fa-plus-circle"></i>
                    </button>
                  }
                </div>
              </div>
            }
          </div>
        }
      }
    </form>
      </div>
    }
  </div>
} @else {
  <div class="success-message">
    Record inserted successfully
  </div>
}
