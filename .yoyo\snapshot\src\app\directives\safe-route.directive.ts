import { Directive, Input, HostListener, inject } from '@angular/core';
import { NavigationService } from '../services/navigation.service';

@Directive({
  selector: '[appSafeRoute]',
  standalone: true
})
export class SafeRouteDirective {
  @Input('appSafeRoute') route: string = '';
  @Input() queryParams?: any;

  private navigationService = inject(NavigationService);

  @HostListener('click', ['$event'])
  onClick(event: Event): void {
    event.preventDefault(); // Prevent default link behavior
    event.stopPropagation();
    
    if (this.route) {
      this.navigationService.navigateTo(this.route, this.queryParams);
    }
  }
}
