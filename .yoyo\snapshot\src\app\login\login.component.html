<div class="login-container">
  <!-- Left Side - Animated Background -->
  <div class="left-side">
    <div class="animated-background" [class.loaded]="animationsLoaded">
      <!-- Floating geometric elements -->
      <div class="floating-element circle circle-1"></div>
      <div class="floating-element circle circle-2"></div>
      <div class="floating-element circle circle-3"></div>

      <div class="floating-element square square-1"></div>
      <div class="floating-element square square-2"></div>

      <div class="floating-element dot dot-1"></div>
      <div class="floating-element dot dot-2"></div>
      <div class="floating-element dot dot-3"></div>

      <div class="floating-element arrow arrow-1">›</div>
      <div class="floating-element arrow arrow-2">▷</div>

      <div class="floating-element line line-1"></div>
      <div class="floating-element line line-2"></div>

      <div class="floating-element triangle triangle-1">▲</div>
      <div class="floating-element triangle triangle-2">▼</div>

      <div class="floating-element plus plus-1">+</div>
      <div class="floating-element plus plus-2">+</div>
    </div>

    <!-- Welcome Text -->
    <div class="welcome-text">
      <h1 class="hello">Hello,</h1>
      <h1 class="welcome">Welcome!</h1>
    </div>
  </div>

  <!-- Right Side - Login Form -->
  <div class="right-side">
    <mat-card class="login-card">
      <mat-card-content>
        <!-- Company Logo -->
        <div class="logo-container">
          <img src="assets/images/offical-logo-2.png" alt="Ultimate Solutions Logo" class="logo">
        </div>

        <!-- Error Message Display -->
        @if (errorMessage) {
          <div class="error-message">
            <mat-icon>error</mat-icon>
            {{ errorMessage }}
          </div>
        }

        <!-- Login Form -->
        <form [formGroup]="loginForm" (ngSubmit)="onSubmit()" class="login-form">
          <!-- Username Field -->
          <mat-form-field appearance="outline" class="full-width">
            <mat-label>Enter your username</mat-label>
            <mat-icon matPrefix>person</mat-icon>
            <input matInput
                   type="text"
                   formControlName="username"
                   [attr.aria-label]="'Username'"
                   autocomplete="username"
                   placeholder="Your username">
            @if (loginForm.get('username')?.hasError('required') && loginForm.get('username')?.touched) {
              <mat-error>{{ getErrorMessage('username') }}</mat-error>
            }
          </mat-form-field>

          <!-- Password Field -->
          <mat-form-field appearance="outline" class="full-width">
            <mat-label>Password</mat-label>
            <mat-icon matPrefix>lock</mat-icon>
            <input matInput
                   [type]="hidePassword ? 'password' : 'text'"
                   formControlName="password"
                   [attr.aria-label]="'Password'"
                   autocomplete="current-password"
                   placeholder="Your password"
                   style="-webkit-text-security: none;"
                   [attr.data-lpignore]="true"
                   [attr.data-form-type]="'other'"
                   class="no-browser-password-toggle">
            <button mat-icon-button
                    matSuffix
                    type="button"
                    (click)="togglePasswordVisibility()"
                    [attr.aria-label]="hidePassword ? 'Show password' : 'Hide password'"
                    [attr.aria-pressed]="!hidePassword">
              <mat-icon>{{ hidePassword ? 'visibility' : 'visibility_off' }}</mat-icon>
            </button>
            @if (loginForm.get('password')?.hasError('required') && loginForm.get('password')?.touched) {
              <mat-error>{{ getErrorMessage('password') }}</mat-error>
            }
          </mat-form-field>

          <!-- Remember Me -->
          <div class="form-options">
            <mat-checkbox formControlName="rememberMe">Remember me</mat-checkbox>
          </div>

          <!-- Submit Button -->
          <button mat-raised-button
                  type="submit"
                  class="login-button full-width"
                  [disabled]="isLoading">
            @if (isLoading) {
              <mat-spinner diameter="20"></mat-spinner>
            } @else {
              Sign In
            }
          </button>
        </form>
      </mat-card-content>
    </mat-card>
  </div>
</div>
