/**
 * DYNAMIC DROPDOWN COMPONENT
 * 
 * This component provides a unified dropdown interface for various types of dynamic form fields.
 * It supports multiple dropdown types: 'type', 'foreignKey', 'regular', and 'id'.
 * 
 * FEATURES:
 * - Server-side and client-side filtering
 * - API response caching for performance
 * - Debounced search functionality
 * - Support for preloaded data
 * - Form control integration
 * - Accessibility features
 * 
 * USAGE:
 * <app-dropdown 
 *   [fieldName]="'fieldName'"
 *   [formControl]="formControl"
 *   [config]="dropdownConfig"
 *   [isDisabled]="false"
 *   [isReadonly]="false"
 *   (valueChange)="onValueChange($event)">
 * </app-dropdown>
 */

import { Component, Input, Output, EventEmitter, OnDestroy, OnInit, OnChanges, SimpleChanges, inject, ChangeDetectorRef } from '@angular/core';
import { FormGroup, FormControl } from '@angular/forms';
import { HttpClient } from '@angular/common/http';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { environment } from '../../../../environments/environment';

/**
 * Represents a single dropdown option with ROW_ID and dynamic properties
 * ROW_ID is always required for backend storage
 * Additional properties are used for display and filtering
 */
export interface DropdownOption {
  ROW_ID: string;  // Primary identifier for backend storage
  [key: string]: any;  // Dynamic properties for display and filtering
}

/**
 * Configuration object for dropdown behavior and appearance
 * Controls API endpoints, search behavior, and UI elements
 */
export interface DropdownConfig {
  type: 'type' | 'foreignKey' | 'regular' | 'id';  // Determines API endpoint and behavior
  apiEndpoint?: string;  // Custom API endpoint (optional)
  queryBuilderId?: string;  // Query builder identifier for API calls
  searchEnabled?: boolean;  // Enable/disable search functionality
  placeholder?: string;  // Input placeholder text
  emptyMessage?: string;  // Message shown when no options found
  tooltip?: string;  // Tooltip text for the dropdown
  maxHeight?: string;  // Maximum height of dropdown list
  limit?: number;  // Maximum number of options to fetch (for ID dropdowns)
}

/**
 * Event emitted when a dropdown option is selected
 * Contains field information, selected value, and display text
 */
export interface DropdownValueChangeEvent {
  fieldName: string;  // Name of the form field
  value: any;  // Selected value (usually ROW_ID)
  option: DropdownOption;  // Complete selected option object
  displayText: string;  // Human-readable display text
}

/**
 * Dynamic Dropdown Component
 * 
 * Provides a unified dropdown interface for dynamic forms with the following capabilities:
 * - Multiple dropdown types (type, foreignKey, regular, id)
 * - Server-side and client-side filtering
 * - API response caching for performance optimization
 * - Form control integration with Angular Reactive Forms
 * - Debounced search with configurable delay
 * - Support for preloaded data to reduce API calls
 * - Accessibility features and keyboard navigation
 * 
 * PERFORMANCE FEATURES:
 * - API response caching to minimize server requests
 * - Debounced search to reduce API calls during typing
 * - Preloaded data support for instant dropdown population
 * - TrackBy functions for efficient Angular change detection
 * 
 * SECURITY:
 * - Uses withCredentials for authenticated API calls
 * - Input validation and sanitization
 * - Proper error handling for failed API requests
 */
@Component({
  selector: 'app-dropdown',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatIconModule,
    MatTooltipModule
  ],
  templateUrl: './dropdown.component.html',
  styleUrl: './dropdown.component.scss'
})
export class DropdownComponent implements OnInit, OnDestroy, OnChanges {
  
  // ==================== CORE INPUTS ====================
  
  /** Name of the form field this dropdown represents */
  @Input() fieldName!: string;
  
  /** Angular FormControl for reactive form integration */
  @Input() formControl!: FormControl;
  
  /** Configuration object defining dropdown behavior and appearance */
  @Input() config!: DropdownConfig;
  
  /** Whether the dropdown is disabled (non-interactive) */
  @Input() isDisabled: boolean = false;
  
  /** Whether the dropdown is readonly (display only) */
  @Input() isReadonly: boolean = false;
  
  /** Pre-defined options array (alternative to API loading) */
  @Input() options: DropdownOption[] = [];
  
  /** Currently selected value */
  @Input() selectedValue: any = '';
  
  /** Additional CSS classes for styling */
  @Input() cssClass: string = '';
  
  /** Unique ID for the input element (for accessibility) */
  @Input() inputId?: string;

  // ==================== ADVANCED CONFIGURATION ====================
  
  /** Preloaded data cache to avoid API calls */
  @Input() preloadedData: { [key: string]: DropdownOption[] } = {};
  
  /** Array of field definitions for extracting original field names */
  @Input() fields: any[] = [];
  
  /** Debounce time for search input (milliseconds) */
  @Input() searchDebounceTime: number = 300;
  
  /** Whether to show the dropdown arrow button */
  @Input() showArrowButton: boolean = true;
  
  /** Whether dropdown should auto-close when clicking outside */
  @Input() autoClose: boolean = true;

  // ==================== OUTPUT EVENTS ====================
  
  /** Emitted when a dropdown option is selected */
  @Output() valueChange = new EventEmitter<DropdownValueChangeEvent>();
  
  /** Emitted when search term changes (debounced) */
  @Output() searchChange = new EventEmitter<string>();
  
  /** Emitted when dropdown opens/closes */
  @Output() dropdownToggle = new EventEmitter<boolean>();
  
  /** Emitted when an option is selected (before value change) */
  @Output() optionSelect = new EventEmitter<DropdownOption>();

  // ==================== INTERNAL STATE ====================
  
  /** Whether dropdown is currently visible */
  showDropdown: boolean = false;
  
  /** Filtered options to display in dropdown */
  filteredOptions: DropdownOption[] = [];
  
  /** Loading state for API calls */
  isLoading: boolean = false;
  
  /** Timeout reference for debounced search */
  searchTimeout: any;
  
  // ==================== PERFORMANCE OPTIMIZATION ====================
  
  /** Cache for API responses to avoid redundant server calls */
  private apiCache: { [key: string]: DropdownOption[] } = {};
  
  /** Flag to prevent input conflicts when programmatically setting values */
  private settingDropdownValue: boolean = false;

  // ==================== DEPENDENCY INJECTION ====================
  
  private http = inject(HttpClient);
  private cdr = inject(ChangeDetectorRef);

  /**
   * LIFECYCLE: Component Initialization
   * 
   * Sets up the component with preloaded data, initializes filtered options,
   * and ensures proper form control state.
   */
  ngOnInit() {
    // Initialize with preloaded data if available
    if (this.preloadedData && Object.keys(this.preloadedData).length > 0) {
      this.apiCache = { ...this.preloadedData };
    }

    // Preload dropdown data for performance optimization
    this.preloadDropdownData();

    // Set initial filtered options if options are provided
    if (this.options && this.options.length > 0) {
      this.filteredOptions = [...this.options];
    }

    // Update disabled state based on current inputs
    this.updateFormControlDisabledState();
  }

  /**
   * LIFECYCLE: Input Changes Detection
   * 
   * Responds to changes in component inputs and updates internal state accordingly.
   * 
   * @param changes - Angular SimpleChanges object containing changed inputs
   */
  ngOnChanges(changes: SimpleChanges): void {
    // Update form control disabled state when disabled/readonly inputs change
    if (changes['isDisabled'] || changes['isReadonly']) {
      this.updateFormControlDisabledState();
    }

    // Update filtered options when options input changes
    if (changes['options'] && this.options) {
      this.filteredOptions = [...this.options];
    }

    // Update cache when preloaded data changes
    if (changes['preloadedData'] && this.preloadedData) {
      this.apiCache = { ...this.preloadedData };
    }
  }

  /**
   * LIFECYCLE: Component Cleanup
   * 
   * Cleans up timeouts and subscriptions to prevent memory leaks.
   */
  ngOnDestroy() {
    // Clear search timeout to prevent memory leaks
    if (this.searchTimeout) {
      clearTimeout(this.searchTimeout);
    }
  }

  // ==================== FORM CONTROL MANAGEMENT ====================
  
  /**
   * Updates the disabled state of the form control based on component inputs
   * Ensures form control state matches component disabled/readonly state
   */
  private updateFormControlDisabledState(): void {
    if (this.formControl) {
      if (this.isDisabled || this.isReadonly) {
        if (this.formControl.enabled) {
          this.formControl.disable();
        }
      } else {
        if (this.formControl.disabled) {
          this.formControl.enable();
        }
      }
    }
  }

  // ==================== DROPDOWN INTERACTION ====================
  
  /**
   * Toggles the dropdown visibility and loads options if needed
   * Handles both opening and closing of the dropdown
   */
  toggleDropdown(): void {
    // Prevent interaction when disabled/readonly
    if (this.isDisabled || this.isReadonly) {
      return;
    }

    if (!this.showDropdown) {
      // Opening dropdown - load options based on current input value
      const currentValue = this.formControl?.value || '';
      if (currentValue.trim() === '') {
        this.loadAllOptions();
      } else {
        this.searchOptions(currentValue);
      }
    } else {
      // Closing dropdown
      this.showDropdown = false;
    }

    // Emit dropdown toggle event
    this.dropdownToggle.emit(this.showDropdown);
  }

  /**
   * Handles input changes with debounced search
   * Prevents conflicts when programmatically setting dropdown values
   * 
   * @param event - Input change event
   */
  onInputChange(event: Event): void {
    // Prevent conflicts when setting dropdown values programmatically
    if (this.settingDropdownValue) {
      return;
    }

    const target = event.target as HTMLInputElement;
    const searchTerm = target.value;

    // Clear existing timeout to implement debouncing
    if (this.searchTimeout) {
      clearTimeout(this.searchTimeout);
    }

    // Debounce search to reduce API calls during typing
    this.searchTimeout = setTimeout(() => {
      this.searchOptions(searchTerm);
      this.searchChange.emit(searchTerm);
    }, this.searchDebounceTime);
  }

  /**
   * Handles input focus events
   * Auto-opens dropdown on focus if not disabled
   */
  onInputFocus(): void {
    // Auto-open dropdown on focus if not disabled
    if (!this.isDisabled && !this.isReadonly && !this.showDropdown) {
      this.toggleDropdown();
    }
  }

  /**
   * Handles input blur events
   * Delays hiding dropdown to allow clicking on dropdown items
   */
  onInputBlur(): void {
    // Delay hiding dropdown to allow click on dropdown items
    if (this.autoClose) {
      setTimeout(() => {
        this.showDropdown = false;
        this.dropdownToggle.emit(false);
      }, 200);
    }
  }

  /**
   * Handles option selection from dropdown
   * Sets the selected value and emits events
   * 
   * @param option - Selected dropdown option
   */
  selectOption(option: DropdownOption): void {
    this.setDropdownValue(option);
    this.optionSelect.emit(option);
  }

  // ==================== SEARCH AND FILTERING ====================
  
  /**
   * Searches for options based on the provided search term
   * Uses different strategies for different dropdown types
   * 
   * @param searchTerm - Search term to filter options
   */
  searchOptions(searchTerm: string): void {
    if (searchTerm.trim() === '') {
      this.loadAllOptions();
      return;
    }

    // For ID dropdowns, use server-side filtering for better performance
    if (this.config.type === 'id') {
      this.loadFromApi(searchTerm);
      return;
    }

    // For other dropdown types, use client-side filtering
    // This provides better UX while we debug server-side filtering
    this.loadAllAndFilter(searchTerm);
  }

  /**
   * Loads all available options for the dropdown
   * Uses cached data if available, otherwise makes API call
   */
  loadAllOptions(): void {
    const cacheKey = this.getCacheKey();

    // Use preloaded data for superior performance
    if (this.apiCache[cacheKey]) {
      this.filteredOptions = this.apiCache[cacheKey];
      this.showDropdown = true;
      return;
    }

    // Fallback: Load if not preloaded
    this.loadFromApi();
  }

  // ==================== CACHE MANAGEMENT ====================
  
  /**
   * Generates a cache key based on dropdown type and configuration
   * Used for storing and retrieving cached API responses
   * 
   * @returns Cache key string
   */
  private getCacheKey(): string {
    if (this.config.queryBuilderId) {
      return this.config.queryBuilderId;
    }
    
    switch (this.config.type) {
      case 'type':
        return 'fieldType';
      case 'foreignKey':
        return 'formDefinition';
      case 'regular':
        // Extract foreign key from field configuration
        const originalFieldName = this.extractOriginalFieldName(this.fieldName);
        const field = this.fields.find(f => f.fieldName === originalFieldName);
        return field?.foreginKey || 'unknown';
      case 'id':
        return this.config.queryBuilderId || 'id';
      default:
        return 'default';
    }
  }

  // ==================== API INTEGRATION ====================
  
  /**
   * Loads options from API with optional search filtering
   * Handles server-side filtering for ID dropdowns
   * 
   * @param searchTerm - Optional search term for filtering
   */
  private loadFromApi(searchTerm?: string): void {
    const apiUrl = this.getApiUrl();
    const payload = this.getApiPayload(searchTerm);

    if (!apiUrl) {
      this.setEmptyDropdownState();
      return;
    }

    this.isLoading = true;

    this.http.post<any[]>(apiUrl, payload, { withCredentials: true }).subscribe({
      next: (response: any) => {
        this.isLoading = false;
        
        if (Array.isArray(response)) {
          // Only cache if no search term (full data)
          if (!searchTerm || searchTerm.trim() === '') {
            const cacheKey = this.getCacheKey();
            this.apiCache[cacheKey] = response;
          }
          this.filteredOptions = response;
          this.showDropdown = true;
        } else {
          this.setEmptyDropdownState();
        }
      },
      error: (error) => {
        this.isLoading = false;
        this.setEmptyDropdownState();
      }
    });
  }

  /**
   * Constructs the API URL for dropdown data
   * Uses custom endpoint if provided, otherwise uses default query builder
   * 
   * @returns API URL string
   */
  private getApiUrl(): string {
    if (this.config.apiEndpoint) {
      return this.config.apiEndpoint;
    }

    const queryBuilderId = this.getCacheKey();
    return `${environment.baseURL}/api/query-builder/search?queryBuilderId=${queryBuilderId}`;
  }

  /**
   * Builds the API payload for dropdown requests
   * Configures select fields and filtering based on dropdown type
   * 
   * @param searchTerm - Optional search term for server-side filtering
   * @returns API payload object
   */
  private getApiPayload(searchTerm?: string): any {
    const basePayload: any = {};

    // Set the correct _select field based on dropdown type
    if (this.config.type === 'id') {
      basePayload._select = ["ID"];
    } else {
      basePayload._select = ["ROW_ID"];
    }

    // Add server-side filtering ONLY for ID dropdowns
    if (searchTerm && searchTerm.trim() !== '' && this.config.type === 'id') {
      basePayload.ID = {
        CT: searchTerm // CT = Contains operator
      };
    }

    // Only add _limit for ID dropdowns since other queryBuilderIds don't support it
    if (this.config.type === 'id') {
      if (this.config.limit) {
        basePayload._limit = this.config.limit;
      } else {
        basePayload._limit = 20;
      }
    }

    return basePayload;
  }

  /**
   * Loads all data and filters client-side
   * Used for non-ID dropdowns that don't support server-side filtering
   * 
   * @param searchTerm - Search term for client-side filtering
   */
  private loadAllAndFilter(searchTerm: string): void {
    const cacheKey = this.getCacheKey();

    // Use preloaded data for superior performance
    if (this.apiCache[cacheKey]) {
      const filtered = this.apiCache[cacheKey].filter(option => {
        // For Regular dropdowns, search across all properties
        if (this.config.type === 'regular') {
          return Object.values(option).some(value =>
            value && value.toString().toLowerCase().includes(searchTerm.toLowerCase())
          );
        }
        // For Type and Foreign Key dropdowns, filter by ROW_ID only
        else {
          return option.ROW_ID && option.ROW_ID.toLowerCase().includes(searchTerm.toLowerCase());
        }
      });
      this.filteredOptions = filtered;
      this.showDropdown = true;
      return;
    }

    // Fallback: Load all data from API and then filter client-side
    const apiUrl = this.getApiUrl();
    const payload = this.getApiPayload(); // No search term for client-side filtering

    if (!apiUrl) {
      this.setEmptyDropdownState();
      return;
    }

    this.isLoading = true;
    this.http.post<any[]>(apiUrl, payload, { withCredentials: true }).subscribe({
      next: (response: any) => {
        this.isLoading = false;
        
        if (Array.isArray(response)) {
          // Cache the full response for future client-side filtering
          this.apiCache[cacheKey] = response;

          // Filter the response based on search term
          const filtered = response.filter(option => {
            // For Regular dropdowns, search across all properties
            if (this.config.type === 'regular') {
              return Object.values(option).some(value =>
                value && value.toString().toLowerCase().includes(searchTerm.toLowerCase())
              );
            }
            // For Type and Foreign Key dropdowns, filter by ROW_ID only
            else {
              return option.ROW_ID && option.ROW_ID.toLowerCase().includes(searchTerm.toLowerCase());
            }
          });

          this.filteredOptions = filtered;
          this.showDropdown = true;
        } else {
          this.setEmptyDropdownState();
        }
      },
      error: (error) => {
        this.isLoading = false;
        this.setEmptyDropdownState();
      }
    });
  }

  // ==================== STATE MANAGEMENT ====================
  
  /**
   * Sets empty dropdown state when no options are available
   */
  private setEmptyDropdownState(): void {
    this.filteredOptions = [];
    this.showDropdown = true;
  }

  /**
   * Sets the selected value in the dropdown and updates form control
   * Handles both display text and storage value
   * 
   * @param option - Selected dropdown option
   */
  private setDropdownValue(option: DropdownOption): void {
    // Mark that we're setting a dropdown value to prevent input conflicts
    this.settingDropdownValue = true;

    if (this.formControl) {
      // Get the display text for the input field
      const displayText = this.getOptionDisplayText(option);

      // Set the form control value to ROW_ID (for storage)
      this.formControl.setValue(option.ROW_ID);

      // Set the input element's display value to the human-readable text
      setTimeout(() => {
        const inputElement = document.getElementById(this.inputId || this.fieldName) as HTMLInputElement;
        if (inputElement) {
          inputElement.value = displayText;
        }
      }, 0);

      // Force change detection and validation
      this.formControl.markAsDirty();
      this.formControl.markAsTouched();
      this.formControl.updateValueAndValidity();

      // Force Angular change detection
      this.cdr.detectChanges();
    }

    // Close dropdown
    this.showDropdown = false;

    // Emit value change event
    const displayText = this.getOptionDisplayText(option);
    this.valueChange.emit({
      fieldName: this.fieldName,
      value: option.ROW_ID,
      option: option,
      displayText: displayText
    });

    // Clear the dropdown value setting flag after a short delay
    setTimeout(() => {
      this.settingDropdownValue = false;
    }, 100);
  }

  // ==================== UTILITY METHODS ====================
  
  /**
   * Get the display text for an option (human-readable text to show in input)
   * Different dropdown types use different properties for display
   * 
   * @param option - Dropdown option object
   * @returns Human-readable display text
   */
  private getOptionDisplayText(option: DropdownOption): string {
    if (!option) return '';

    // For ID fields, return the ID property
    if (this.config.type === 'id') {
      return option['ID'] || option.ROW_ID || '';
    }

    // For type fields, return the ROW_ID
    if (this.config.type === 'type') {
      return option.ROW_ID || '';
    }

    // For other foreign key fields, get the first non-ROW_ID property
    const keys = Object.keys(option).filter(key => key !== 'ROW_ID');
    if (keys.length > 0) {
      return option[keys[0]] || option.ROW_ID || '';
    }

    return option.ROW_ID || '';
  }

  /**
   * Extract the original field name from complex field names
   * Handles various naming patterns used in dynamic forms:
   * - fieldName_group_k
   * - fieldName_nested_k_n
   * - fieldName_group_k_multi_l
   * - fieldName_j (for multi-fields)
   * 
   * @param fieldName - Complex field name to parse
   * @returns Original field name
   */
  private extractOriginalFieldName(fieldName: string): string {
    if (fieldName.includes('_nested_')) {
      return fieldName.split('_nested_')[0];
    } else if (fieldName.includes('_group_')) {
      return fieldName.split('_group_')[0];
    } else if (fieldName.includes('_')) {
      // Handle simple multi-field pattern like fieldName_j
      const parts = fieldName.split('_');
      if (parts.length === 2 && !isNaN(parseInt(parts[1]))) {
        return parts[0];
      }
      return fieldName;
    }
    return fieldName;
  }

  /**
   * Preloads dropdown data for performance optimization
   * Loads data in background to reduce latency when dropdown is opened
   */
  private preloadDropdownData(): void {
    const cacheKey = this.getCacheKey();

    // Skip if already cached
    if (this.apiCache[cacheKey]) {
      return;
    }

    const apiUrl = this.getApiUrl();
    const payload = this.getApiPayload();

    if (!apiUrl) {
      return;
    }

    this.http.post<any[]>(apiUrl, payload, { withCredentials: true }).subscribe({
      next: (response: any) => {
        if (Array.isArray(response)) {
          this.apiCache[cacheKey] = response;
        }
      },
      error: () => {
        // Handle preload error silently
      }
    });
  }

  // ==================== TEMPLATE UTILITY METHODS ====================
  
  /**
   * Get all keys from an option object for template iteration
   * 
   * @param option - Dropdown option object
   * @returns Array of property keys
   */
  getKeys(option: DropdownOption): string[] {
    return Object.keys(option);
  }

  /**
   * TrackBy function for performance optimization in ngFor loops
   * Uses appropriate identifier based on dropdown type
   * 
   * @param _index - Array index (unused)
   * @param option - Dropdown option object
   * @returns Unique identifier for the option
   */
  trackByOptionId(_index: number, option: DropdownOption): string {
    // For ID dropdowns, use ID field; for others, use ROW_ID
    if (this.config.type === 'id') {
      return option['ID'] || option.ROW_ID || '';
    }
    return option.ROW_ID || '';
  }

  /**
   * TrackBy function for key iteration in templates
   * 
   * @param _index - Array index (unused)
   * @param key - Property key
   * @returns The key itself as identifier
   */
  trackByKey(_index: number, key: string): string {
    return key;
  }

  // ==================== COMPUTED PROPERTIES ====================
  
  /**
   * Computed CSS class for the input element
   * Includes base form-input class and additional styling
   * 
   * @returns CSS class string
   */
  get inputClass(): string {
    // Start with base form-input class
    let classes = 'form-input';

    // Add any additional CSS classes passed in
    if (this.cssClass) {
      // If cssClass already contains 'form-input', don't duplicate it
      if (this.cssClass.includes('form-input')) {
        classes = this.cssClass;
      } else {
        classes += ` ${this.cssClass}`;
      }
    }

    // Add disabled state if needed
    if (this.isDisabled || this.isReadonly) {
      classes += ' disabled';
    }

    return classes;
  }

  /**
   * Computed icon for dropdown arrow
   * Changes based on dropdown open/closed state
   * 
   * @returns Material icon name
   */
  get dropdownArrowIcon(): string {
    return this.showDropdown ? 'keyboard_arrow_up' : 'keyboard_arrow_down';
  }

  /**
   * Computed empty message for dropdown
   * Uses config value or default message
   * 
   * @returns Empty message string
   */
  get emptyMessage(): string {
    return this.config.emptyMessage || 'No options found';
  }

  /**
   * Computed placeholder text for input
   * Uses config value or empty string
   * 
   * @returns Placeholder text string
   */
  get placeholderText(): string {
    return this.config.placeholder || '';
  }

  /**
   * Computed tooltip text
   * Uses config value or default tooltip
   * 
   * @returns Tooltip text string
   */
  get tooltipText(): string {
    return this.config.tooltip || 'Show options';
  }

  /**
   * Computed maximum height for dropdown list
   * Uses config value or default height
   * 
   * @returns CSS height value
   */
  get dropdownMaxHeight(): string {
    return this.config.maxHeight || '200px';
  }
}
