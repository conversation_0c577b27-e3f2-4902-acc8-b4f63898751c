<!-- 
  FORM HEADER COMPONENT TEMPLATE
  
  This template provides the header section of dynamic forms with action buttons
  and form state management. It serves as the primary navigation and control
  interface for form operations.
  
  STRUCTURE:
  - Form header container with responsive layout
  - ID display field showing current record ID
  - Action buttons grouped in a container
  - Error message display when needed
  - Responsive design for different screen sizes
  
  FEATURES:
  - Action buttons for different form operations
  - View mode toggle functionality
  - Error message display
  - Responsive design with Material Design components
  - Authorization and validation support
  
  BUTTON OPERATIONS:
  - Delete: Removes the current record
  - Reject: Rejects the record in authorization workflow
  - Back: Navigates to previous screen
  - Authorize: Approves the record in authorization workflow
  - Validate: Validates form data before submission
  - Submit: Saves the form data
  - Toggle View: Switches between view modes
  
  VIEW MODES:
  - Edit Mode: Full form interaction with all buttons
  - View Mode: Read-only display with limited buttons
  - Row View: Compact display mode
  - Nested View: Detailed display mode
  
  ACCESSIBILITY:
  - Proper tooltips for all buttons
  - Material Design icons for visual clarity
  - Keyboard navigation support
  - Screen reader compatibility
-->

<!-- Main form header container -->
<div class="form-header">
  
  <!-- 
    HORIZONTAL CONTAINER
    - Flexbox layout for responsive alignment
    - Space-between for proper distribution
    - Responsive gap and padding
    - Clean white background
  -->
  <div class="horizontal-container">
    
    <!-- 
      FORM FIELD SECTION
      - Displays the current record ID
      - Styled as a badge with background
      - Centered text with proper typography
      - Responsive sizing for different screens
    -->
    <div class="form-field">
      <!-- 
        ID DISPLAY
        - Shows the current record ID from form
        - Styled as a prominent badge
        - Brand color for visual emphasis
        - Responsive font sizing
      -->
      <p>{{ form.get('ID')?.value }}</p>
    </div>

    <!-- 
      BUTTON GROUP SECTION
      - Container for all action buttons
      - Flexbox layout with proper spacing
      - Responsive wrapping behavior
      - Error message integration
    -->
    <div class="button-group">
      
      <!-- 
        BUTTON CONTAINER
        - Groups action buttons with rounded corners
        - Light background with subtle shadow
        - Horizontal scrolling for overflow
        - Proper spacing and responsive behavior
      -->
      <div class="button-container">
        
        <!-- 
          DELETE BUTTON
          - Red theme for destructive action
          - Only visible in edit mode
          - Material Design delete icon
          - Tooltip for accessibility
          - Readonly state support
        -->
        @if (!isViewMode) {
          <button mat-raised-button color="warn" type="button" matTooltip="Delete"
                  class="form-action-button delete-button" (click)="onDeleteRecord()"
                  [ngClass]="{ 'readonly-button': isAuth }">
            <mat-icon>delete</mat-icon>
          </button>
        }

        <!-- 
          REJECT BUTTON
          - Red theme for rejection action
          - Only visible in edit mode
          - Material Design cancel icon
          - Tooltip for accessibility
          - Readonly state support
        -->
        @if (!isViewMode) {
          <button mat-raised-button color="warn" type="button" matTooltip="Reject"
                  class="form-action-button reject-button" (click)="onRejectRecord()"
                  [ngClass]="{ 'readonly-button': isAuth }">
            <mat-icon>cancel</mat-icon>
          </button>
        }

        <!-- 
          BACK BUTTON
          - Gray theme for navigation
          - Always visible for navigation
          - Material Design arrow back icon
          - Tooltip for accessibility
          - Primary navigation action
        -->
        <button mat-raised-button color="primary" type="button" (click)="onGoBack()" matTooltip="Back"
                class="form-action-button back-button">
          <mat-icon>arrow_back</mat-icon>
        </button>

        <!-- 
          AUTHORIZE BUTTON
          - Purple theme for authorization
          - Only visible in edit mode
          - Material Design verified icon
          - Tooltip for accessibility
          - Authorization workflow action
        -->
        @if (!isViewMode) {
          <button mat-raised-button color="accent" type="button" (click)="onAuthorizeRecord()" matTooltip="Authorize"
                  class="form-action-button authorize-button">
            <mat-icon>verified</mat-icon>
          </button>
        }

        <!-- 
          VALIDATE BUTTON
          - Blue theme for validation
          - Only visible in edit mode
          - Material Design check circle icon
          - Tooltip for accessibility
          - Form validation action
        -->
        @if (!isViewMode) {
          <button mat-raised-button color="accent" type="button" (click)="onValidateRecord()" matTooltip="Validate"
                  class="form-action-button validate-button">
            <mat-icon>check_circle</mat-icon>
          </button>
        }

        <!-- 
          SUBMIT BUTTON
          - Green theme for submission
          - Only visible in edit mode
          - Material Design send icon
          - Tooltip for accessibility
          - Disabled in view mode
          - Primary form submission action
        -->
        @if (!isViewMode) {
          <button mat-raised-button color="primary" type="submit" [disabled]="isViewMode" matTooltip="Submit"
                  class="form-action-button submit-button" (click)="onSubmitForm()">
            <mat-icon>send</mat-icon>
          </button>
        }

        <!-- 
          VIEW TOGGLE BUTTON
          - Teal theme for view switching
          - Always visible for view control
          - Dynamic icon based on current view
          - Dynamic text based on current view
          - Tooltip for accessibility
          - Toggles between Row View and Nested View
        -->
        <button mat-raised-button color="primary" type="button" (click)="onToggleViewMode()" matTooltip="Toggle View"
                class="form-action-button toggle-view-button">
          <mat-icon>{{ isRowView ? 'view_list' : 'view_module' }}</mat-icon>
          {{ isRowView ? 'Nested View' : 'Row View' }}
        </button>
      </div>
      
      <!-- 
        ERROR MESSAGE
        - Displays error messages when present
        - Red styling for error indication
        - Responsive text sizing
        - Proper spacing and alignment
        - Only visible when error exists
      -->
      @if (errorMessage) {
        <div class="error-message">{{ errorMessage }}</div>
      }
    </div>
  </div>
</div>
