/**
 * UNIFIED DROPDOWN COMPONENT STYLES
 * 
 * This SCSS file provides comprehensive styling for the unified dropdown component
 * with support for various states, responsive design, and accessibility features.
 * 
 * DESIGN SYSTEM:
 * - Uses Poppins font family for consistency
 * - Material Design color palette
 * - Consistent border radius (8px) and spacing
 * - Smooth transitions for better UX
 * 
 * COMPONENT STATES:
 * - Default: Clean, modern input with subtle borders
 * - Focus: Enhanced border color with box shadow
 * - Invalid: Red border with light red background
 * - Disabled: Grayed out with reduced opacity
 * - Hover: Subtle background color changes
 * 
 * RESPONSIVE DESIGN:
 * - Mobile-first approach
 * - Breakpoints at 768px and 480px
 * - Adjusted sizing for smaller screens
 * - Touch-friendly interactions
 * 
 * ACCESSIBILITY:
 * - High contrast ratios
 * - Focus indicators
 * - Screen reader compatibility
 * - Keyboard navigation support
 */

@use '../../../../styles/shared.scss' as *;

/* ==================== MAIN CONTAINER ==================== */

/**
 * DROPDOWN INPUT CONTAINER
 * 
 * Main wrapper for the dropdown component with:
 * - Relative positioning for absolute dropdown positioning
 * - Flexbox layout for input and button alignment
 * - Responsive width with minimum constraints
 * - Disabled state support
 */
.dropdown-input-container {
  position: relative;
  display: flex;
  align-items: center;
  width: 100%;
  min-width: 200px;
}

/* ==================== INPUT FIELD STYLING ==================== */

/**
 * BASE FORM INPUT STYLING
 * 
 * Consistent styling for the search input field:
 * - 40px height for touch-friendly interaction
 * - Rounded corners with 8px border radius
 * - Subtle border with light gray color
 * - Poppins font for modern typography
 * - Smooth transitions for state changes
 * - Proper padding and box sizing
 */
.dropdown-input-container .form-input {
  flex: 1;
  height: 40px;
  border: 1px solid #DBDBDB;
  border-radius: 8px 0 0 8px; /* Left side rounded, right side flat for button */
  border-right: none; /* Remove right border for seamless button connection */
  font-family: 'Poppins', sans-serif;
  font-size: 16px;
  color: #222222;
  background: #FFFFFF;
  transition: all 0.3s ease;
  padding: 0 12px;
  outline: none;
  box-sizing: border-box;
}

/**
 * FOCUS STATE
 * 
 * Enhanced visual feedback when input is focused:
 * - Gray border color for subtle indication
 * - Box shadow for depth and emphasis
 * - Maintains accessibility standards
 */
.dropdown-input-container .form-input:focus {
  outline: none;
  border-color: #9e9e9e;
  box-shadow: 0 0 0 2px rgba(158, 158, 158, 0.25);
}

/**
 * INVALID INPUT STATE
 * 
 * Error state styling for validation feedback:
 * - Red border color for clear error indication
 * - Light red background for subtle emphasis
 * - Enhanced focus state with red shadow
 */
.dropdown-input-container .form-input.invalid-input {
  border-color: #f44336;
  background-color: rgba(244, 67, 54, 0.05);
}

.dropdown-input-container .form-input.invalid-input:focus {
  box-shadow: 0 0 0 2px rgba(244, 67, 54, 0.2);
}

/**
 * PLACEHOLDER STYLING
 * 
 * Subtle placeholder text styling:
 * - Light gray color for reduced emphasis
 * - Maintains readability while being unobtrusive
 */
.dropdown-input-container .form-input::placeholder {
  color: #999;
}

/* ==================== ARROW BUTTON STYLING ==================== */

/**
 * DROPDOWN ARROW BUTTON
 * 
 * Toggle button for dropdown visibility:
 * - 40px square for touch-friendly interaction
 * - Positioned absolutely on the right side
 * - Connected seamlessly to input field
 * - Light background with subtle hover effects
 * - Material Design icon integration
 */
.dropdown-input-container .dropdown-arrow-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border: 1px solid #DBDBDB;
  border-left: none; /* Seamless connection with input */
  border-radius: 0 8px 8px 0; /* Right side rounded, left side flat */
  background-color: #f8f9fa;
  color: #495057;
  cursor: pointer;
  transition: all 0.3s ease;
  padding: 0;
  margin: 0;
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
}

/**
 * ARROW BUTTON HOVER STATE
 * 
 * Interactive feedback on hover:
 * - Darker background for visual feedback
 * - Brand color for icon emphasis
 * - Smooth transition for polished UX
 */
.dropdown-input-container .dropdown-arrow-btn:hover {
  background-color: #e9ecef;
  color: #283A97;
}

/**
 * ARROW BUTTON FOCUS STATE
 * 
 * Accessibility-focused styling:
 * - Box shadow for keyboard navigation
 * - Consistent with input focus styling
 * - Maintains accessibility standards
 */
.dropdown-input-container .dropdown-arrow-btn:focus {
  outline: none;
  box-shadow: 0 0 0 0.2rem rgba(158, 158, 158, 0.25);
}

/**
 * ARROW BUTTON ICON
 * 
 * Material Design icon styling:
 * - 20px size for optimal visibility
 * - Proper line height for centering
 * - Consistent with design system
 */
.dropdown-input-container .dropdown-arrow-btn .mat-icon {
  font-size: 20px;
  width: 20px;
  height: 20px;
  line-height: 1;
}

/* ==================== DROPDOWN LIST STYLING ==================== */

/**
 * DROPDOWN LIST CONTAINER
 * 
 * Container for dropdown options:
 * - Absolutely positioned below input
 * - High z-index for proper layering
 * - White background with subtle shadow
 * - Rounded corners for modern appearance
 * - Scrollable with configurable max height
 */
.dropdown-list {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  z-index: 1000;
  background-color: white;
  border: 1px solid #DBDBDB;
  border-top: none; /* Seamless connection with input */
  border-radius: 0 0 8px 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  max-height: 200px;
  overflow-y: auto;
}

/**
 * DROPDOWN ITEM STYLING
 * 
 * Individual option items:
 * - Comfortable padding for touch interaction
 * - Subtle borders between items
 * - Hover effects for interactivity
 * - Consistent typography with design system
 */
.dropdown-item {
  padding: 12px 16px;
  cursor: pointer;
  border-bottom: 1px solid #f1f3f4;
  font-family: 'Poppins', sans-serif;
  font-size: 14px;
  color: #495057;
  transition: background-color 0.2s ease;
}

/**
 * DROPDOWN ITEM HOVER STATE
 * 
 * Interactive feedback for option selection:
 * - Light background color change
 * - Brand color for text emphasis
 * - Smooth transition for polished UX
 */
.dropdown-item:hover {
  background-color: #f8f9fa;
  color: #283A97;
}

/**
 * LAST DROPDOWN ITEM
 * 
 * Removes border from last item for clean appearance
 */
.dropdown-item:last-child {
  border-bottom: none;
}

/**
 * EMPTY STATE STYLING
 * 
 * Styling for when no options are available:
 * - Centered text alignment
 * - Italic styling for emphasis
 * - Muted color for reduced prominence
 */
.dropdown-empty {
  padding: 12px 16px;
  color: #6c757d;
  font-style: italic;
  text-align: center;
}

/**
 * LOADING STATE STYLING
 * 
 * Styling for loading indicator:
 * - Centered layout with flexbox
 * - Gap between icon and text
 * - Consistent padding with other states
 * - Muted color for subtle appearance
 */
.dropdown-loading {
  padding: 12px 16px;
  color: #6c757d;
  font-style: italic;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

/**
 * LOADING ICON ANIMATION
 * 
 * Spinning animation for loading state:
 * - 1 second linear infinite rotation
 * - Smaller icon size for subtle appearance
 * - Smooth animation for professional feel
 */
.dropdown-loading .mat-icon {
  animation: spin 1s linear infinite;
  font-size: 16px;
  width: 16px;
  height: 16px;
}

/**
 * SPIN ANIMATION KEYFRAMES
 * 
 * Defines the spinning animation for loading states
 */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* ==================== DISABLED STATE STYLING ==================== */

/**
 * DISABLED INPUT STYLING
 * 
 * Comprehensive disabled state for input field:
 * - Gray background for visual indication
 * - Muted text color for reduced emphasis
 * - Disabled cursor for clear interaction state
 * - Gray border for consistent appearance
 * - Disabled pointer events for proper behavior
 */
.dropdown-input-container.disabled .form-input,
.dropdown-input-container .form-input.disabled,
.dropdown-input-container .form-input[disabled],
.dropdown-input-container .form-input[readonly] {
  background-color: #f1f3f4 !important;
  color: #5f6368 !important;
  cursor: not-allowed !important;
  border-color: #dadce0 !important;
  opacity: 1 !important;
  pointer-events: none !important;
}

/**
 * DISABLED ARROW BUTTON STYLING
 * 
 * Disabled state for arrow button:
 * - Consistent with input disabled styling
 * - Disabled cursor and pointer events
 * - Grayed out appearance
 */
.dropdown-input-container.disabled .dropdown-arrow-btn,
.dropdown-input-container .dropdown-arrow-btn[disabled] {
  background-color: #f1f3f4 !important;
  border-color: #dadce0 !important;
  cursor: not-allowed !important;
  pointer-events: none !important;
}

/**
 * DISABLED ARROW BUTTON ICON
 * 
 * Muted icon color for disabled state
 */
.dropdown-input-container.disabled .dropdown-arrow-btn .mat-icon,
.dropdown-input-container .dropdown-arrow-btn[disabled] .mat-icon {
  color: #5f6368 !important;
}

/* ==================== CONTEXT-SPECIFIC STYLING ==================== */

/**
 * MULTI-INPUT AND GROUP FIELDS CONTEXT
 * 
 * Ensures dropdown works well in different form contexts:
 * - Full width in multi-input scenarios
 * - Minimum width for usability
 * - Consistent behavior across contexts
 */
.multi-input .dropdown-input-container,
.group-fields .dropdown-input-container {
  width: 100%;
  min-width: 200px;
}

/**
 * GROUPED FIELD SECTION Z-INDEX
 * 
 * Higher z-index for dropdowns in grouped fields:
 * - Ensures proper layering in complex layouts
 * - Prevents overlap issues with other elements
 */
.grouped-field-section .dropdown-list {
  z-index: 1001;
}

/* ==================== GLOBAL FORM INPUT STYLING ==================== */

/**
 * GLOBAL FORM INPUT STYLES
 * 
 * Base styles for all form-input elements:
 * - Consistent with dropdown input styling
 * - Standalone input support
 * - Design system compliance
 */
.form-input {
  height: 40px;
  border: 1px solid #DBDBDB;
  border-radius: 8px;
  font-family: 'Poppins', sans-serif;
  font-size: 16px;
  color: #222222;
  background: #FFFFFF;
  transition: all 0.3s ease;
  padding: 0 12px;
  outline: none;
  box-sizing: border-box;
}

.form-input:focus {
  outline: none;
  border-color: #9e9e9e;
  box-shadow: 0 0 0 2px rgba(158, 158, 158, 0.25);
}

.form-input.invalid-input {
  border-color: #f44336;
  background-color: rgba(244, 67, 54, 0.05);
}

.form-input.invalid-input:focus {
  box-shadow: 0 0 0 2px rgba(244, 67, 54, 0.2);
}

.form-input::placeholder {
  color: #999;
}

/* ==================== RESPONSIVE DESIGN ==================== */

/**
 * TABLET RESPONSIVE STYLING (max-width: 768px)
 * 
 * Adjustments for tablet-sized screens:
 * - Smaller button and icon sizes
 * - Reduced padding for space efficiency
 * - Maintained touch-friendly interactions
 */
@media (max-width: 768px) {
  .dropdown-input-container .dropdown-arrow-btn {
    width: 36px;
    height: 36px;
  }

  .dropdown-input-container .dropdown-arrow-btn .mat-icon {
    font-size: 18px;
    width: 18px;
    height: 18px;
  }

  .dropdown-item {
    padding: 10px 12px;
    font-size: 13px;
  }

  .dropdown-empty {
    padding: 10px 12px;
    font-size: 13px;
  }

  .dropdown-loading {
    padding: 10px 12px;
    font-size: 13px;
  }
}

/**
 * MOBILE RESPONSIVE STYLING (max-width: 480px)
 * 
 * Adjustments for mobile-sized screens:
 * - Further reduced sizes for mobile optimization
 * - Compact padding for space efficiency
 * - Maintained usability on small screens
 */
@media (max-width: 480px) {
  .dropdown-input-container .dropdown-arrow-btn {
    width: 32px;
    height: 32px;
  }

  .dropdown-input-container .dropdown-arrow-btn .mat-icon {
    font-size: 16px;
    width: 16px;
    height: 16px;
  }

  .dropdown-item {
    padding: 8px 10px;
    font-size: 12px;
  }

  .dropdown-empty {
    padding: 8px 10px;
    font-size: 12px;
  }

  .dropdown-loading {
    padding: 8px 10px;
    font-size: 12px;
  }
}

/* ==================== SCROLLBAR STYLING ==================== */

/**
 * CUSTOM SCROLLBAR STYLING
 * 
 * Webkit scrollbar customization for dropdown lists:
 * - Thin 6px scrollbar for modern appearance
 * - Rounded corners for polished look
 * - Hover effects for interactivity
 * - Consistent with design system colors
 */
.dropdown-list::-webkit-scrollbar {
  width: 6px;
}

.dropdown-list::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.dropdown-list::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.dropdown-list::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* ==================== CONTEXT-SPECIFIC OVERRIDES ==================== */

/**
 * MULTI-INPUT AND GROUP FIELDS CONTEXT OVERRIDES
 * 
 * Ensures proper styling in complex form layouts:
 * - Full width utilization in multi-input scenarios
 * - Minimum width for usability
 * - Consistent behavior across different contexts
 */
.multi-input .dropdown-input-container,
.group-fields .dropdown-input-container {
  width: 100%;
  min-width: 200px;
}

/**
 * GROUPED FIELD SECTION Z-INDEX OVERRIDE
 * 
 * Higher z-index for complex layouts:
 * - Prevents layering issues in grouped fields
 * - Ensures dropdown visibility in all contexts
 */
.grouped-field-section .dropdown-list {
  z-index: 1001;
}
