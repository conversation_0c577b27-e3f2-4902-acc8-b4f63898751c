/**
 * INITIAL INPUT COMPONENT
 * 
 * This component provides the initial input interface for dynamic forms, typically used
 * for ID selection before loading the full form. It integrates with the unified dropdown
 * component to provide a consistent user experience.
 * 
 * PURPOSE:
 * - Allows users to select an ID to load existing records
 * - Provides action buttons for Add, Edit, View, and Maintenance operations
 * - Handles form validation and state management
 * - Integrates with the dynamic form system
 * 
 * FEATURES:
 * - ID dropdown with search functionality
 * - Action buttons for different operations
 * - Form validation integration
 * - Responsive design with Material Design components
 * 
 * USAGE:
 * <app-initial-input 
 *   [form]="form"
 *   [tableName]="'tableName'"
 *   [screenName]="'screenName'"
 *   [showValidation]="false"
 *   (loadDataAndBuildForm)="onLoadData()"
 *   (viewData)="onViewData()"
 *   (validationChange)="onValidationChange($event)">
 * </app-initial-input>
 */

import { Component, Input, Output, EventEmitter, OnInit, OnDestroy, inject } from '@angular/core';
import { FormGroup, ReactiveFormsModule } from '@angular/forms';
import { HttpClient } from '@angular/common/http';
import { CommonModule } from '@angular/common';
import { environment } from '../../../../environments/environment';

// Angular Material imports for UI components
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';

// Dropdown component import for ID selection
import { DropdownComponent, DropdownConfig, DropdownValueChangeEvent } from '../dropdown/dropdown.component';

/**
 * Initial Input Component
 * 
 * Provides the entry point for dynamic forms where users can:
 * - Select an existing record by ID
 * - Add new records
 * - Edit existing records
 * - View record details
 * - Access maintenance functions
 * 
 * INTEGRATION:
 * - Uses unified DropdownComponent for ID selection
 * - Integrates with Angular Reactive Forms
 * - Emits events for parent component handling
 * - Supports validation state management
 * 
 * VALIDATION:
 * - Tracks validation state for ID input
 * - Emits validation changes to parent component
 * - Provides visual feedback for invalid inputs
 */
@Component({
  selector: 'app-initial-input',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatButtonModule,
    MatIconModule,
    MatTooltipModule,
    DropdownComponent
  ],
  templateUrl: './initial-input.component.html',
  styleUrl: './initial-input.component.scss'
})
export class InitialInputComponent implements OnInit, OnDestroy {
  
  // ==================== CORE INPUTS ====================
  
  /** Angular FormGroup for reactive form integration */
  @Input() form!: FormGroup;
  
  /** Name of the database table for API operations */
  @Input() tableName!: string;
  
  /** Name of the screen/form for API operations */
  @Input() screenName!: string;
  
  /** Whether to show validation state for the ID input */
  @Input() showValidation: boolean = false;

  // ==================== OUTPUT EVENTS ====================
  
  /** Emitted when user wants to load data and build the full form */
  @Output() loadDataAndBuildForm = new EventEmitter<void>();
  
  /** Emitted when user wants to view existing data */
  @Output() viewData = new EventEmitter<void>();
  
  /** Emitted when validation state changes */
  @Output() validationChange = new EventEmitter<boolean>();

  // ==================== DEPENDENCY INJECTION ====================
  
  /** HTTP client for API operations */
  private http = inject(HttpClient);

  /**
   * LIFECYCLE: Component Initialization
   * 
   * Sets up the component and initializes any required state.
   * Note: Most functionality is now handled by the unified DropdownComponent.
   */
  ngOnInit() {
    // Component initialization
    // Note: ID dropdown functionality moved to unified DropdownComponent
  }

  /**
   * LIFECYCLE: Component Cleanup
   * 
   * Performs cleanup operations when component is destroyed.
   * Note: Cleanup is primarily handled by the unified DropdownComponent.
   */
  ngOnDestroy() {
    // Cleanup handled by unified DropdownComponent
  }

  // ==================== VALIDATION HELPERS ====================
  
  /**
   * Gets the CSS class for the input element based on validation state
   * Applies invalid-input class when validation is shown
   * 
   * @returns CSS class string for input styling
   */
  getInputClass(): string {
    return this.showValidation ? 'invalid-input' : '';
  }

  // ==================== API HELPERS ====================
  
  /**
   * Extracts the query builder ID from table/screen name
   * Handles comma-separated values by taking the first part
   * 
   * @param tableName - Optional table name override
   * @returns Query builder ID string
   */
  private extractQueryBuilderId(tableName?: string): string {
    const nameToUse = tableName || this.screenName || this.tableName;
    if (nameToUse && nameToUse.includes(',')) {
      return nameToUse.split(',')[0].trim();
    }
    return nameToUse;
  }

  // ==================== ACTION HANDLERS ====================
  
  /**
   * Handles Add button click
   * Emits event to load data and build form for new record
   */
  onAddClick(): void {
    this.loadDataAndBuildForm.emit();
  }

  /**
   * Handles Edit button click
   * Emits event to load data and build form for editing
   */
  onEditClick(): void {
    this.loadDataAndBuildForm.emit();
  }

  /**
   * Handles View button click
   * Emits event to view existing data
   */
  onViewClick(): void {
    this.viewData.emit();
  }

  /**
   * Handles Maintenance button click
   * Placeholder for maintenance functionality
   * TODO: Implement maintenance functionality
   */
  onMaintenanceClick(): void {
    // Placeholder for maintenance functionality
    // TODO: Implement maintenance operations
  }

  // ==================== DROPDOWN CONFIGURATION ====================
  
  /**
   * Creates configuration for the ID dropdown component
   * Configures the dropdown for ID selection with appropriate settings
   * 
   * @returns DropdownConfig object for ID dropdown
   */
  getIdDropdownConfig(): DropdownConfig {
    return {
      type: 'id',  // ID dropdown type
      queryBuilderId: this.extractQueryBuilderId(),  // Extract from table/screen name
      searchEnabled: true,  // Enable search functionality
      placeholder: 'Enter ID',  // User-friendly placeholder
      emptyMessage: 'No IDs found',  // Message when no options available
      tooltip: 'Show ID suggestions',  // Tooltip for accessibility
      limit: 100  // Maximum number of IDs to fetch
    };
  }

  // ==================== STYLING HELPERS ====================
  
  /**
   * Gets the correct CSS class for the ID input including validation state
   * Combines base form-input class with validation styling
   * 
   * @returns CSS class string for ID input
   */
  getIdInputClass(): string {
    let classes = 'form-input';
    if (this.showValidation) {
      classes += ' invalid-input';
    }
    return classes;
  }

  // ==================== DROPDOWN EVENT HANDLERS ====================
  
  /**
   * Handles value changes from the unified dropdown component
   * Resets validation state when a valid ID is selected
   * 
   * @param event - Dropdown value change event
   */
  onDropdownValueChange(event: DropdownValueChangeEvent): void {
    // The form control is already updated by the dropdown component
    // Reset validation state when a valid ID is selected
    if (event.value && event.value.trim() !== '') {
      this.showValidation = false;
      this.validationChange.emit(false);
    }
  }

  /**
   * Handles search input changes from the dropdown component
   * Clears validation state when user starts typing
   * 
   * @param searchTerm - Current search term
   */
  onSearchChange(searchTerm: string): void {
    // Clear validation state when user starts typing
    if (this.showValidation) {
      this.showValidation = false;
      this.validationChange.emit(false);
    }
  }

  // ==================== FORM CONTROL HELPERS ====================
  
  /**
   * Helper method to get FormControl with proper typing
   * Provides type-safe access to form controls
   * 
   * @param fieldName - Name of the form field
   * @returns FormControl for the specified field
   */
  getFormControl(fieldName: string): any {
    return this.form.get(fieldName);
  }
}
