<!-- ======================================== -->
<!-- DYNAMIC FORM COMPONENT TEMPLATE -->
<!-- ======================================== -->
<!-- 
  This template handles the complete dynamic form rendering including:
  - Form actions (submit, validate, authorize, etc.)
  - Initial ID input for data loading
  - Dynamic field rendering based on metadata
  - Multi-field support with add/remove functionality
  - Grouped fields with nested groups
  - Row view and nested view modes
  - Error and success message display
-->

<!-- ======================================== -->
<!-- FORM ACTIONS COMPONENT -->
<!-- ======================================== -->
<!-- 
  Handles all form actions like submit, validate, authorize, reject, delete
  Communicates with parent component through event emitters
  Manages form state and user interactions
-->
<app-form-actions #formActions
  [form]="form"
  [tableName]="tableName"
  [screenName]="screenName"
  [isTenantBasedFlag]="isTenantBasedFlag"
  [authorizeNumber]="authorizeNumber"
  [isViewMode]="isViewMode"
  [showSuccessPopup]="showSuccessPopup"
  [successMessage]="successMessage"
  [errorMessage]="errorMessage"
  [isLoading]="isLoading"
  [validationResult]="validationResult"
  [fields]="fields"
  (submissionSuccess)="onSubmissionSuccess($event)"
  (errorMessageChange)="onErrorMessageChange($event)"
  (isLoadingChange)="onIsLoadingChange($event)"
  (showSuccessPopupChange)="onShowSuccessPopupChange($event)"
  (successMessageChange)="onSuccessMessageChange($event)"
  (validationResultChange)="onValidationResultChange($event)"
  (goBackRequested)="onGoBackRequested()"
  (setFormReadonly)="onSetFormReadonly($event)"
  (populateForm)="onPopulateForm($event)"
  (populateDefaultFields)="onPopulateDefaultFields($event)"
  (setViewMode)="onSetViewMode($event)">
</app-form-actions>

<!-- ======================================== -->
<!-- MAIN FORM CONTENT (Conditional Rendering) -->
<!-- ======================================== -->
<!-- Only show form content if submission is not successful -->
@if (!submissionSuccess) {
  
  <!-- ======================================== -->
  <!-- INITIAL INPUT SECTION -->
  <!-- ======================================== -->
  <!-- 
    Shows when user needs to enter an ID to load existing data
    Handles ID validation and triggers data loading
  -->
  @if (showInitialInput) {
    <app-initial-input
      [form]="form"
      [tableName]="tableName"
      [screenName]="screenName"
      [showValidation]="showValidation"
      (loadDataAndBuildForm)="loadDataAndBuildForm()"
      (viewData)="viewData()"
      (validationChange)="onValidationChange($event)">
    </app-initial-input>
  }

  <!-- ======================================== -->
  <!-- ERROR MESSAGE DISPLAY -->
  <!-- ======================================== -->
  <!-- Shows error messages from API calls or validation -->
  @if (errorMessage) {
    <div class="error-message">{{ errorMessage }}</div>
  }

  <!-- ======================================== -->
  <!-- MAIN FORM CONTENT -->
  <!-- ======================================== -->
  <!-- 
    Shows the actual form after ID is entered and data is loaded
    Contains all dynamic fields, groups, and form controls
  -->
  @if (!showInitialInput) {
    <div class="form-grid">
    <form [formGroup]="form">
      
      <!-- ======================================== -->
      <!-- FORM HEADER COMPONENT -->
      <!-- ======================================== -->
      <!-- 
        Contains form title, view/edit toggle, and action buttons
        Handles form submission, validation, and navigation
      -->
      <app-form-header
        [form]="form"
        [isViewMode]="isViewMode"
        [isAuth]="isAuth"
        [isRowView]="isRowView"
        [errorMessage]="errorMessage"
        (toggleViewMode)="toggleViewMode()"
        (submitForm)="onFormSubmit()"
        (validateRecord)="onFormValidate()"
        (authorizeRecord)="onFormAuthorize()"
        (goBack)="goBack()"
        (rejectRecord)="onFormReject()"
        (deleteRecord)="onFormDelete()">
      </app-form-header>
      
      <!-- ======================================== -->
      <!-- SUB-SCREEN TABS -->
      <!-- ======================================== -->
      <!-- 
        Shows tabs when sub-screens are present
        Each tab represents a different sub-screen form
      -->
      @if (hasSubScreens) {
        <mat-tab-group 
          [selectedIndex]="activeSubScreenIndex"
          (selectedIndexChange)="switchSubScreen($event)"
          class="sub-screen-tabs">
          
          <!-- Main Screen Tab -->
          <mat-tab label="Main Screen">
            <div class="form-grid" [ngClass]="'columns-' + columnCount">
              <!-- Main screen content will be rendered here -->
              <ng-container *ngTemplateOutlet="mainScreenContent"></ng-container>
            </div>
          </mat-tab>
          
          <!-- Sub-Screen Tabs -->
          @for (subScreen of subScreens; track subScreen.id; let i = $index) {
            <mat-tab [label]="subScreen.title">
              <div class="form-grid" [ngClass]="'columns-' + subScreen.metadata.columnNumber">
                <!-- Sub-screen content will be rendered here -->
                <ng-container *ngTemplateOutlet="subScreenContent; context: { $implicit: subScreen }"></ng-container>
              </div>
            </mat-tab>
          }
        </mat-tab-group>
      }

      <!-- ======================================== -->
      <!-- MAIN SCREEN CONTENT (when no sub-screens) -->
      <!-- ======================================== -->
      <!-- 
        Shows main form content when no sub-screens are present
        Uses the original layout and field rendering
      -->
      @if (!hasSubScreens) {
        <div class="form-grid" [ngClass]="'columns-' + columnCount">
        
        <!-- ======================================== -->
        <!-- COLUMN ITERATION -->
        <!-- ======================================== -->
        <!-- 
          Iterates through columns to create responsive layout
          Each column contains a subset of fields
        -->
        <div class="form-column" *ngFor="let column of columns">
          
          <!-- ======================================== -->
          <!-- FIELD ITERATION WITHIN COLUMN -->
          <!-- ======================================== -->
          <!-- 
            Iterates through fields in each column
            Skips ID field as it's handled separately in header
          -->
          <ng-container *ngFor="let field of column">
          <!-- Skip ID field as it's handled separately -->
          @if (field.fieldName?.toUpperCase() !== 'ID') {

                <!-- ======================================== -->
                <!-- NON-GROUPED FIELDS SECTION -->
                <!-- ======================================== -->
                <!-- 
                  Handles fields that are not part of any group
                  Includes both regular fields and multi-fields
                -->
                @if (!field.Group) {

                  <!-- ======================================== -->
                  <!-- REGULAR FIELD COMPONENT (NON-MULTI) -->
                  <!-- ======================================== -->
                  <!-- 
                    Renders single fields (text, number, date, etc.)
                    Uses RegularFieldComponent for consistent styling
                  -->
                  @if (!field.isMulti) {
                    <app-regular-field
                      [field]="field"
                      [form]="form"
                      [isViewMode]="isViewMode"
                      [fields]="fields"
                      (fieldValueChange)="onFieldValueChange($event)">
                    </app-regular-field>
                  }

                  <!-- ======================================== -->
                  <!-- MULTI FIELD COMPONENT SECTION -->
                  <!-- ======================================== -->
                  <!-- 
                    Handles fields that can have multiple instances
                    Allows adding/removing field instances dynamically
                    Each instance is a separate form group
                  -->
                  @if (field.isMulti) {
                    <div [formArrayName]="field.fieldName">
                      @for (control of getMultiArray(field.fieldName).controls; track control; let j = $index) {
                        <div [formGroupName]="j" class="form-field is-multi">
                          <app-regular-field
                            [field]="field"
                            [form]="$any(control)"
                            [isViewMode]="isViewMode"
                            [fields]="fields"
                            [multiIndex]="j + 1"
                            (fieldValueChange)="onFieldValueChange($event)">
                          </app-regular-field>

                          <!-- ======================================== -->
                          <!-- MULTI FIELD ACTION BUTTONS -->
                          <!-- ======================================== -->
                          <!-- 
                            Add/Remove buttons for multi-fields
                            Only show remove if more than 1 instance exists
                            Hide buttons in view mode or if field has noInput flag
                          -->
                          <div class="multi-buttons">
                            @if (getMultiArray(field.fieldName).length > 1 && !isViewMode && !field.noInput) {
                              <button mat-icon-button color="warn" type="button" (click)="removeMultiField(field.fieldName, j)" matTooltip="Delete">
                                <mat-icon>delete</mat-icon>
                              </button>
                            }

                            @if (!isViewMode && !field.noInput) {
                              <button mat-icon-button color="primary" type="button" (click)="addMultiField(field, undefined, j)" matTooltip="Add">
                                <mat-icon>add</mat-icon>
                              </button>
                            }
                          </div>
                        </div>
                      }
                    </div>
                  }
                }

                <!-- ======================================== -->
                <!-- GROUPED FIELDS COMPONENT SECTION -->
                <!-- ======================================== -->
                <!-- 
                  Handles fields that are part of groups (like ledgers)
                  Supports nested groups and different view modes
                  Only renders the first field of each group to avoid duplication
                -->
                @if (field.Group && isFirstFieldInParentGroup(field)) {
                  @let parsed = parseGroupPath(field.Group);
                  @if (parsed.parent) {
                    <div [formArrayName]="parsed.parent" class="grouped-field-section">
                      <h3>{{ parsed.parent }}</h3>
                      @for (group of getGroupArray(parsed.parent).controls; track group; let k = $index) {
                        <div [formGroupName]="k" [class]="isRowView ? 'form-grid row-view-table' : 'form-grid multi-field'">

                          @if (isRowView) {
                            <!-- ======================================== -->
                            <!-- ROW VIEW MODE -->
                            <!-- ======================================== -->
                            <!-- 
                              Displays grouped fields in a table-like format
                              All fields from a group appear in a single row
                              Better for viewing multiple group instances
                            -->
                            <div class="row-view-table-container">
                              <!-- Parent group fields -->
                              @for (groupField of getFieldsForGroup(parsed.parent); track groupField.fieldName) {
                                @if (!groupField.isMulti) {
                                  <div class="row-view-table-cell">
                                    <app-regular-field
                                      [field]="groupField"
                                      [form]="$any(group)"
                                      [isViewMode]="isViewMode"
                                      [fields]="fields"
                                      (fieldValueChange)="onFieldValueChange($event)">
                                    </app-regular-field>
                                  </div>
                                }

                                <!-- Multi-fields for parent group -->
                                @if (groupField.isMulti) {
                                  <div class="row-view-table-cell-multi" [formArrayName]="groupField.fieldName">
                                    <label>{{ groupField.fieldName }}</label>
                                    @for (multiControl of getMultiArray(groupField.fieldName, k, field.Group).controls; track multiControl; let l = $index) {
                                      <div [formGroupName]="l" class="row-view-multi-item">
                                        <div class="row-view-multi-input">
                                          <app-regular-field
                                            [field]="groupField"
                                            [form]="$any(multiControl)"
                                            [isViewMode]="isViewMode"
                                            [fields]="fields"
                                            [multiIndex]="l + 1"
                                            (fieldValueChange)="onFieldValueChange($event)">
                                          </app-regular-field>
                                        </div>
                                        <div class="row-view-multi-buttons">
                                          @if (getMultiArray(groupField.fieldName, k, field.Group).controls.length > 1 && !isViewMode && !groupField.noInput) {
                                            <button mat-icon-button color="warn" type="button" (click)="removeMultiField(groupField.fieldName, l, k, field.Group)" matTooltip="Delete">
                                              <mat-icon>delete</mat-icon>
                                            </button>
                                          }
                                          @if (!isViewMode && !groupField.noInput) {
                                            <button mat-icon-button color="primary" type="button" (click)="addMultiField(groupField, k, l, field.Group)" matTooltip="Add">
                                              <mat-icon>add</mat-icon>
                                            </button>
                                          }
                                        </div>
                                      </div>
                                    }
                                  </div>
                                }
                              }

                              <!-- ======================================== -->
                              <!-- NESTED GROUP FIELDS (ROW VIEW) -->
                              <!-- ======================================== -->
                              <!-- 
                                Handles nested groups within parent groups
                                Each nested group is rendered as additional cells in the row
                              -->
                              @for (childGroup of getChildGroups(parsed.parent); track childGroup) {
                                @for (nestedGroup of getNestedGroupArray(parsed.parent + '|' + childGroup, k).controls; track nestedGroup; let n = $index) {
                                  @for (nestedField of getFieldsForGroupPath(parsed.parent + '|' + childGroup); track nestedField.fieldName) {
                                    @if (!nestedField.isMulti) {
                                      <div class="row-view-table-cell" [formArrayName]="childGroup" [formGroupName]="n">
                                        <app-regular-field
                                          [field]="nestedField"
                                          [form]="$any(nestedGroup)"
                                          [isViewMode]="isViewMode"
                                          [fields]="fields"
                                          (fieldValueChange)="onFieldValueChange($event)">
                                        </app-regular-field>
                                      </div>
                                    }

                                    <!-- Multi-fields for nested groups -->
                                    @if (nestedField.isMulti) {
                                      <div class="row-view-table-cell-multi" [formArrayName]="childGroup" [formGroupName]="n">
                                        <div [formArrayName]="nestedField.fieldName">
                                          <label>{{ nestedField.fieldName }}</label>
                                          @for (multiControl of getMultiArray(nestedField.fieldName, k, parsed.parent + '|' + childGroup, n).controls; track multiControl; let m = $index) {
                                            <div [formGroupName]="m" class="row-view-multi-item">
                                              <div class="row-view-multi-input">
                                                <app-regular-field
                                                  [field]="nestedField"
                                                  [form]="$any(multiControl)"
                                                  [isViewMode]="isViewMode"
                                                  [fields]="fields"
                                                  [multiIndex]="m + 1"
                                                  (fieldValueChange)="onFieldValueChange($event)">
                                                </app-regular-field>
                                              </div>
                                              <div class="row-view-multi-buttons">
                                                @if (getMultiArray(nestedField.fieldName, k, parsed.parent + '|' + childGroup, n).controls.length > 1 && !isViewMode && !nestedField.noInput) {
                                                  <button mat-icon-button color="warn" type="button" (click)="removeMultiField(nestedField.fieldName, m, k, parsed.parent + '|' + childGroup, n)" matTooltip="Delete">
                                                    <mat-icon>delete</mat-icon>
                                                  </button>
                                                }
                                                @if (!isViewMode && !nestedField.noInput) {
                                                  <button mat-icon-button color="primary" type="button" (click)="addMultiField(nestedField, k, m, parsed.parent + '|' + childGroup, n)" matTooltip="Add">
                                                    <mat-icon>add</mat-icon>
                                                  </button>
                                                }
                                              </div>
                                            </div>
                                          }
                                        </div>
                                      </div>
                                    }
                                  }

                                  <!-- Nested group control buttons -->
                                  <div class="row-view-table-actions" [formArrayName]="childGroup" [formGroupName]="n">
                                    @if (!isViewMode) {
                                      <button mat-icon-button color="warn" type="button" (click)="removeNestedGroup(parsed.parent + '|' + childGroup, k, n)" matTooltip="Remove {{ childGroup }} Group" [disabled]="getNestedGroupArray(parsed.parent + '|' + childGroup, k).controls.length <= 1">
                                        <mat-icon>delete</mat-icon>
                                      </button>
                                    }
                                    @if (!isViewMode) {
                                      <button mat-icon-button color="primary" type="button" (click)="addNestedGroup(parsed.parent + '|' + childGroup, k, n)" matTooltip="Add {{ childGroup }} Group">
                                        <mat-icon>add</mat-icon>
                                      </button>
                                    }
                                    @if (!isViewMode) {
                                      <button mat-icon-button color="accent" type="button" (click)="cloneNestedGroup(parsed.parent + '|' + childGroup, k, n)" matTooltip="Clone {{ childGroup }} Group">
                                        <mat-icon>content_copy</mat-icon>
                                      </button>
                                    }
                                  </div>
                                }
                              }

                              <!-- Main group action buttons -->
                              <div class="row-view-table-actions">
                                @if (!isViewMode) {
                                  <button mat-icon-button color="warn" type="button" (click)="removeGroup(parsed.parent, k)" matTooltip="Remove Group" [disabled]="getGroupArray(parsed.parent).controls.length <= 1">
                                    <mat-icon>delete</mat-icon>
                                  </button>
                                  <button mat-icon-button color="primary" type="button" (click)="addGroup(parsed.parent, k)" matTooltip="Add Group">
                                    <mat-icon>add</mat-icon>
                                  </button>
                                  <button mat-icon-button color="accent" type="button" (click)="cloneGroup(parsed.parent, k)" matTooltip="Clone Group">
                                    <mat-icon>content_copy</mat-icon>
                                  </button>
                                }
                              </div>
                            </div>
                          } @else {
                            <!-- Nested View: Original layout -->
                            <div [class]="isRowView ? 'group-fields row-view-fields' : 'group-fields'">

                              <!-- Direct fields of parent group -->
                              @for (groupField of getFieldsForGroup(parsed.parent); track groupField.fieldName) {

                                @if (!groupField.isMulti) {
                                  <app-regular-field
                                    [field]="groupField"
                                    [form]="$any(group)"
                                    [isViewMode]="isViewMode"
                                    [fields]="fields"
                                    (fieldValueChange)="onFieldValueChange($event)">
                                  </app-regular-field>
                                }

                                @if (groupField.isMulti) {
                                  <div [formArrayName]="groupField.fieldName">
                                    <h4>{{ groupField.fieldName }}</h4>

                                    @for (multiControl of getMultiArray(groupField.fieldName, k, field.Group).controls; track multiControl; let l = $index) {
                                      <div [formGroupName]="l" class="form-field">
                                        <app-regular-field
                                          [field]="groupField"
                                          [form]="$any(multiControl)"
                                          [isViewMode]="isViewMode"
                                          [fields]="fields"
                                          [multiIndex]="l + 1"
                                          (fieldValueChange)="onFieldValueChange($event)">
                                        </app-regular-field>

                                        @if (getMultiArray(groupField.fieldName, k, field.Group).controls.length > 1 && !isViewMode && !groupField.noInput) {
                                          <button mat-icon-button color="warn" type="button" (click)="removeMultiField(groupField.fieldName, l, k, field.Group)" matTooltip="Delete">
                                            <mat-icon>delete</mat-icon>
                                          </button>
                                        }

                                        @if (!isViewMode && !groupField.noInput) {
                                          <button mat-icon-button color="primary" type="button" (click)="addMultiField(groupField, k, l, field.Group)" matTooltip="Add">
                                            <mat-icon>add</mat-icon>
                                          </button>
                                        }
                                      </div>
                                    }
                                  </div>
                                }
                              }

                              <!-- Nested subgroups -->
                              @for (childGroup of getChildGroups(parsed.parent); track childGroup) {
                                <div [formArrayName]="childGroup" [class]="isRowView ? 'nested-group-section row-view-nested-seamless' : 'nested-group-section'">
                                  <h4 [class.row-view-hidden]="isRowView">{{ childGroup }}</h4>
                                  @for (nestedGroup of getNestedGroupArray(parsed.parent + '|' + childGroup, k).controls; track nestedGroup; let n = $index) {
                                    <div [formGroupName]="n" [class]="isRowView ? 'form-grid nested-field row-view-nested-field-seamless' : 'form-grid nested-field'">
                                      <div [class]="isRowView ? 'nested-group-fields row-view-nested-fields-seamless' : 'nested-group-fields'">
                                        @for (nestedField of getFieldsForGroupPath(parsed.parent + '|' + childGroup); track nestedField.fieldName) {

                                          @if (!nestedField.isMulti) {
                                            <app-regular-field
                                              [field]="nestedField"
                                              [form]="$any(nestedGroup)"
                                              [isViewMode]="isViewMode"
                                              [fields]="fields"
                                              (fieldValueChange)="onFieldValueChange($event)">
                                            </app-regular-field>
                                          }

                                          @if (nestedField.isMulti) {
                                            <div [formArrayName]="nestedField.fieldName">
                                              <h5>{{ nestedField.fieldName }}</h5>

                                              @for (multiControl of getMultiArray(nestedField.fieldName, k, parsed.parent + '|' + childGroup, n).controls; track multiControl; let m = $index) {
                                                <div [formGroupName]="m" class="form-field is-multi">
                                                  <div class="multi-input-container">
                                                    <app-regular-field
                                                      [field]="nestedField"
                                                      [form]="$any(multiControl)"
                                                      [isViewMode]="isViewMode"
                                                      [fields]="fields"
                                                      [multiIndex]="m + 1"
                                                      (fieldValueChange)="onFieldValueChange($event)">
                                                    </app-regular-field>

                                                    <div class="multi-buttons">
                                                      @if (getMultiArray(nestedField.fieldName, k, parsed.parent + '|' + childGroup, n).controls.length > 1 && !isViewMode && !nestedField.noInput) {
                                                        <button mat-icon-button color="warn" type="button" (click)="removeMultiField(nestedField.fieldName, m, k, parsed.parent + '|' + childGroup, n)" matTooltip="Delete">
                                                          <mat-icon>delete</mat-icon>
                                                        </button>
                                                      }

                                                      @if (!isViewMode && !nestedField.noInput) {
                                                        <button mat-icon-button color="primary" type="button" (click)="addMultiField(nestedField, k, m, parsed.parent + '|' + childGroup, n)" matTooltip="Add">
                                                          <mat-icon>add</mat-icon>
                                                        </button>
                                                      }
                                                    </div>
                                                  </div>
                                                </div>
                                              }
                                            </div>
                                          }
                                        }

                                        <!-- Nested group control buttons -->
                                        <div class="nested-group-buttons">
                                          @if (!isViewMode) {
                                            <button mat-icon-button color="warn" type="button" (click)="removeNestedGroup(parsed.parent + '|' + childGroup, k, n)" matTooltip="Remove {{ childGroup }} Group" [disabled]="getNestedGroupArray(parsed.parent + '|' + childGroup, k).controls.length <= 1">
                                              <mat-icon>delete</mat-icon>
                                            </button>
                                          }
                                          @if (!isViewMode) {
                                            <button mat-icon-button color="primary" type="button" (click)="addNestedGroup(parsed.parent + '|' + childGroup, k, n)" matTooltip="Add {{ childGroup }} Group">
                                              <mat-icon>add</mat-icon>
                                            </button>
                                          }
                                          @if (!isViewMode) {
                                            <button mat-icon-button color="accent" type="button" (click)="cloneNestedGroup(parsed.parent + '|' + childGroup, k, n)" matTooltip="Clone {{ childGroup }} Group">
                                              <mat-icon>content_copy</mat-icon>
                                            </button>
                                          }
                                        </div>
                                      </div>
                                    </div>
                                  }
                                </div>
                              }

                              <!-- Group control buttons for nested view -->
                              <div class="group-buttons">
                                @if (!isViewMode) {
                                  <button mat-icon-button color="warn" type="button" (click)="removeGroup(parsed.parent, k)" matTooltip="Remove Group" [disabled]="getGroupArray(parsed.parent).controls.length <= 1">
                                    <mat-icon>delete</mat-icon>
                                  </button>
                                }
                                @if (!isViewMode) {
                                  <button mat-icon-button color="primary" type="button" (click)="addGroup(parsed.parent, k)" matTooltip="Add Group">
                                    <mat-icon>add</mat-icon>
                                  </button>
                                }
                                @if (!isViewMode) {
                                  <button mat-icon-button color="accent" type="button" (click)="cloneGroup(parsed.parent, k)" matTooltip="Clone Group">
                                    <mat-icon>content_copy</mat-icon>
                                  </button>
                                }
                              </div>
                            </div>
                          }
                        </div>
                      }
                    </div>
                  }
                }
              }
            </ng-container>
          </div>
        </div>
      </form>
    </div>
  }
} @else {
  <div class="success-message">Record submitted successfully!</div>
}

<!-- ======================================== -->
<!-- TEMPLATE REFERENCES -->
<!-- ======================================== -->

<!-- Main Screen Content Template -->
<ng-template #mainScreenContent>
  <!-- ======================================== -->
  <!-- COLUMN ITERATION -->
  <!-- ======================================== -->
  <!-- 
    Iterates through columns to create responsive layout
    Each column contains a subset of fields
  -->
  <div class="form-column" *ngFor="let column of columns">
    
    <!-- ======================================== -->
    <!-- FIELD ITERATION WITHIN COLUMN -->
    <!-- ======================================== -->
    <!-- 
      Iterates through fields in each column
      Skips ID field as it's handled separately in header
    -->
    <ng-container *ngFor="let field of column">
    <!-- Skip ID field as it's handled separately -->
    @if (field.fieldName?.toUpperCase() !== 'ID') {

          <!-- ======================================== -->
          <!-- NON-GROUPED FIELDS SECTION -->
          <!-- ======================================== -->
          <!-- 
            Handles fields that are not part of any group
            Includes both regular fields and multi-fields
          -->
          @if (!field.Group) {

            <!-- ======================================== -->
            <!-- REGULAR FIELD COMPONENT (NON-MULTI) -->
            <!-- ======================================== -->
            <!-- 
              Renders single fields (text, number, date, etc.)
              Uses RegularFieldComponent for consistent styling
            -->
            @if (!field.isMulti) {
              <app-regular-field
                [field]="field"
                [form]="form"
                [isViewMode]="isViewMode"
                [fields]="fields"
                (fieldValueChange)="onFieldValueChange($event)">
              </app-regular-field>
            }

            <!-- ======================================== -->
            <!-- MULTI FIELD COMPONENT SECTION -->
            <!-- ======================================== -->
            <!-- 
              Handles fields that can have multiple instances
              Allows adding/removing field instances dynamically
              Each instance is a separate form group
            -->
            @if (field.isMulti) {
              <div [formArrayName]="field.fieldName">
                @for (control of getMultiArray(field.fieldName).controls; track control; let j = $index) {
                  <div [formGroupName]="j" class="form-field is-multi">
                    <app-regular-field
                      [field]="field"
                      [form]="$any(control)"
                      [isViewMode]="isViewMode"
                      [fields]="fields"
                      [multiIndex]="j + 1"
                      (fieldValueChange)="onFieldValueChange($event)">
                    </app-regular-field>

                    <!-- ======================================== -->
                    <!-- MULTI FIELD ACTION BUTTONS -->
                    <!-- ======================================== -->
                    <!-- 
                      Add/Remove buttons for multi-fields
                      Only show remove if more than 1 instance exists
                      Hide buttons in view mode or if field has noInput flag
                    -->
                    <div class="multi-buttons">
                      @if (getMultiArray(field.fieldName).length > 1 && !isViewMode && !field.noInput) {
                        <button mat-icon-button color="warn" type="button" (click)="removeMultiField(field.fieldName, j)" matTooltip="Delete">
                          <mat-icon>delete</mat-icon>
                        </button>
                      }

                      @if (!isViewMode && !field.noInput) {
                        <button mat-icon-button color="primary" type="button" (click)="addMultiField(field, undefined, j)" matTooltip="Add">
                          <mat-icon>add</mat-icon>
                        </button>
                      }
                    </div>
                  </div>
                }
              </div>
            }
          }

          <!-- ======================================== -->
          <!-- GROUPED FIELDS COMPONENT SECTION -->
          <!-- ======================================== -->
          <!-- 
            Handles fields that are part of groups (like ledgers)
            Supports nested groups and different view modes
            Only renders the first field of each group to avoid duplication
          -->
          @if (field.Group && isFirstFieldInParentGroup(field)) {
            @let parsed = parseGroupPath(field.Group);
            @if (parsed.parent) {
              <div [formArrayName]="parsed.parent" class="grouped-field-section">
                <h3>{{ parsed.parent }}</h3>
                @for (group of getGroupArray(parsed.parent).controls; track group; let k = $index) {
                  <div [formGroupName]="k" [class]="isRowView ? 'form-grid row-view-table' : 'form-grid multi-field'">

                    @if (isRowView) {
                      <!-- ======================================== -->
                      <!-- ROW VIEW MODE -->
                      <!-- ======================================== -->
                      <!-- 
                        Displays grouped fields in a table-like format
                        All fields from a group appear in a single row
                        Better for viewing multiple group instances
                      -->
                      <div class="row-view-table-container">
                        <!-- Parent group fields -->
                        @for (groupField of getFieldsForGroup(parsed.parent); track groupField.fieldName) {
                          @if (!groupField.isMulti) {
                            <div class="row-view-table-cell">
                              <app-regular-field
                                [field]="groupField"
                                [form]="$any(group)"
                                [isViewMode]="isViewMode"
                                [fields]="fields"
                                (fieldValueChange)="onFieldValueChange($event)">
                              </app-regular-field>
                            </div>
                          }

                          <!-- Multi-fields for parent group -->
                          @if (groupField.isMulti) {
                            <div class="row-view-table-cell-multi" [formArrayName]="groupField.fieldName">
                              <label>{{ groupField.fieldName }}</label>
                              @for (multiControl of getMultiArray(groupField.fieldName, k, field.Group).controls; track multiControl; let l = $index) {
                                <div [formGroupName]="l" class="row-view-multi-item">
                                  <div class="row-view-multi-input">
                                    <app-regular-field
                                      [field]="groupField"
                                      [form]="$any(multiControl)"
                                      [isViewMode]="isViewMode"
                                      [fields]="fields"
                                      [multiIndex]="l + 1"
                                      (fieldValueChange)="onFieldValueChange($event)">
                                    </app-regular-field>
                                  </div>
                                  <div class="row-view-multi-buttons">
                                    @if (getMultiArray(groupField.fieldName, k, field.Group).controls.length > 1 && !isViewMode && !groupField.noInput) {
                                      <button mat-icon-button color="warn" type="button" (click)="removeMultiField(groupField.fieldName, l, k, field.Group)" matTooltip="Delete">
                                        <mat-icon>delete</mat-icon>
                                      </button>
                                    }
                                    @if (!isViewMode && !groupField.noInput) {
                                      <button mat-icon-button color="primary" type="button" (click)="addMultiField(groupField, k, l, field.Group)" matTooltip="Add">
                                        <mat-icon>add</mat-icon>
                                      </button>
                                    }
                                  </div>
                                </div>
                              }
                            </div>
                          }
                        }

                        <!-- ======================================== -->
                        <!-- NESTED GROUP FIELDS (ROW VIEW) -->
                        <!-- ======================================== -->
                        <!-- 
                          Handles nested groups within parent groups
                          Each nested group is rendered as additional cells in the row
                        -->
                        @for (childGroup of getChildGroups(parsed.parent); track childGroup) {
                          @for (nestedGroup of getNestedGroupArray(parsed.parent + '|' + childGroup, k).controls; track nestedGroup; let n = $index) {
                            @for (nestedField of getFieldsForGroupPath(parsed.parent + '|' + childGroup); track nestedField.fieldName) {
                              @if (!nestedField.isMulti) {
                                <div class="row-view-table-cell" [formArrayName]="childGroup" [formGroupName]="n">
                                  <app-regular-field
                                    [field]="nestedField"
                                    [form]="$any(nestedGroup)"
                                    [isViewMode]="isViewMode"
                                    [fields]="fields"
                                    (fieldValueChange)="onFieldValueChange($event)">
                                  </app-regular-field>
                                </div>
                              }

                              <!-- Multi-fields for nested groups -->
                              @if (nestedField.isMulti) {
                                <div class="row-view-table-cell-multi" [formArrayName]="childGroup" [formGroupName]="n">
                                  <div [formArrayName]="nestedField.fieldName">
                                    <label>{{ nestedField.fieldName }}</label>
                                    @for (multiControl of getMultiArray(nestedField.fieldName, k, parsed.parent + '|' + childGroup, n).controls; track multiControl; let m = $index) {
                                      <div [formGroupName]="m" class="row-view-multi-item">
                                        <div class="row-view-multi-input">
                                          <app-regular-field
                                            [field]="nestedField"
                                            [form]="$any(multiControl)"
                                            [isViewMode]="isViewMode"
                                            [fields]="fields"
                                            [multiIndex]="m + 1"
                                            (fieldValueChange)="onFieldValueChange($event)">
                                          </app-regular-field>
                                        </div>
                                        <div class="row-view-multi-buttons">
                                          @if (getMultiArray(nestedField.fieldName, k, parsed.parent + '|' + childGroup, n).controls.length > 1 && !isViewMode && !nestedField.noInput) {
                                            <button mat-icon-button color="warn" type="button" (click)="removeMultiField(nestedField.fieldName, m, k, parsed.parent + '|' + childGroup, n)" matTooltip="Delete">
                                              <mat-icon>delete</mat-icon>
                                            </button>
                                          }
                                          @if (!isViewMode && !nestedField.noInput) {
                                            <button mat-icon-button color="primary" type="button" (click)="addMultiField(nestedField, k, m, parsed.parent + '|' + childGroup, n)" matTooltip="Add">
                                              <mat-icon>add</mat-icon>
                                            </button>
                                          }
                                        </div>
                                      </div>
                                    }
                                  </div>
                                </div>
                              }
                            }

                            <!-- Nested group control buttons -->
                            <div class="row-view-table-actions" [formArrayName]="childGroup" [formGroupName]="n">
                              @if (!isViewMode) {
                                <button mat-icon-button color="warn" type="button" (click)="removeNestedGroup(parsed.parent + '|' + childGroup, k, n)" matTooltip="Remove {{ childGroup }} Group" [disabled]="getNestedGroupArray(parsed.parent + '|' + childGroup, k).controls.length <= 1">
                                  <mat-icon>delete</mat-icon>
                                </button>
                              }
                              @if (!isViewMode) {
                                <button mat-icon-button color="primary" type="button" (click)="addNestedGroup(parsed.parent + '|' + childGroup, k, n)" matTooltip="Add {{ childGroup }} Group">
                                  <mat-icon>add</mat-icon>
                                </button>
                              }
                              @if (!isViewMode) {
                                <button mat-icon-button color="accent" type="button" (click)="cloneNestedGroup(parsed.parent + '|' + childGroup, k, n)" matTooltip="Clone {{ childGroup }} Group">
                                  <mat-icon>content_copy</mat-icon>
                                </button>
                              }
                            </div>
                          }
                        }

                        <!-- Main group action buttons -->
                        <div class="row-view-table-actions">
                          @if (!isViewMode) {
                            <button mat-icon-button color="warn" type="button" (click)="removeGroup(parsed.parent, k)" matTooltip="Remove Group" [disabled]="getGroupArray(parsed.parent).controls.length <= 1">
                              <mat-icon>delete</mat-icon>
                            </button>
                            <button mat-icon-button color="primary" type="button" (click)="addGroup(parsed.parent, k)" matTooltip="Add Group">
                              <mat-icon>add</mat-icon>
                            </button>
                            <button mat-icon-button color="accent" type="button" (click)="cloneGroup(parsed.parent, k)" matTooltip="Clone Group">
                              <mat-icon>content_copy</mat-icon>
                            </button>
                          }
                        </div>
                      </div>
                    } @else {
                      <!-- Nested View: Original layout -->
                      <div [class]="isRowView ? 'group-fields row-view-fields' : 'group-fields'">

                        <!-- Direct fields of parent group -->
                        @for (groupField of getFieldsForGroup(parsed.parent); track groupField.fieldName) {

                          @if (!groupField.isMulti) {
                            <app-regular-field
                              [field]="groupField"
                              [form]="$any(group)"
                              [isViewMode]="isViewMode"
                              [fields]="fields"
                              (fieldValueChange)="onFieldValueChange($event)">
                            </app-regular-field>
                          }

                          @if (groupField.isMulti) {
                            <div [formArrayName]="groupField.fieldName">
                              <h4>{{ groupField.fieldName }}</h4>

                              @for (multiControl of getMultiArray(groupField.fieldName, k, field.Group).controls; track multiControl; let l = $index) {
                                <div [formGroupName]="l" class="form-field">
                                  <app-regular-field
                                    [field]="groupField"
                                    [form]="$any(multiControl)"
                                    [isViewMode]="isViewMode"
                                    [fields]="fields"
                                    [multiIndex]="l + 1"
                                    (fieldValueChange)="onFieldValueChange($event)">
                                  </app-regular-field>

                                  @if (getMultiArray(groupField.fieldName, k, field.Group).controls.length > 1 && !isViewMode && !groupField.noInput) {
                                    <button mat-icon-button color="warn" type="button" (click)="removeMultiField(groupField.fieldName, l, k, field.Group)" matTooltip="Delete">
                                      <mat-icon>delete</mat-icon>
                                    </button>
                                  }

                                  @if (!isViewMode && !groupField.noInput) {
                                    <button mat-icon-button color="primary" type="button" (click)="addMultiField(groupField, k, l, field.Group)" matTooltip="Add">
                                      <mat-icon>add</mat-icon>
                                    </button>
                                  }
                                </div>
                              }
                            </div>
                          }
                        }

                        <!-- Nested subgroups -->
                        @for (childGroup of getChildGroups(parsed.parent); track childGroup) {
                          <div [formArrayName]="childGroup" [class]="isRowView ? 'nested-group-section row-view-nested-seamless' : 'nested-group-section'">
                            <h4 [class.row-view-hidden]="isRowView">{{ childGroup }}</h4>
                            @for (nestedGroup of getNestedGroupArray(parsed.parent + '|' + childGroup, k).controls; track nestedGroup; let n = $index) {
                              <div [formGroupName]="n" [class]="isRowView ? 'form-grid nested-field row-view-nested-field-seamless' : 'form-grid nested-field'">
                                <div [class]="isRowView ? 'nested-group-fields row-view-nested-fields-seamless' : 'nested-group-fields'">
                                  @for (nestedField of getFieldsForGroupPath(parsed.parent + '|' + childGroup); track nestedField.fieldName) {

                                    @if (!nestedField.isMulti) {
                                      <app-regular-field
                                        [field]="nestedField"
                                        [form]="$any(nestedGroup)"
                                        [isViewMode]="isViewMode"
                                        [fields]="fields"
                                        (fieldValueChange)="onFieldValueChange($event)">
                                      </app-regular-field>
                                    }

                                    @if (nestedField.isMulti) {
                                      <div [formArrayName]="nestedField.fieldName">
                                        <h5>{{ nestedField.fieldName }}</h5>

                                        @for (multiControl of getMultiArray(nestedField.fieldName, k, parsed.parent + '|' + childGroup, n).controls; track multiControl; let m = $index) {
                                          <div [formGroupName]="m" class="form-field is-multi">
                                            <div class="multi-input-container">
                                              <app-regular-field
                                                [field]="nestedField"
                                                [form]="$any(multiControl)"
                                                [isViewMode]="isViewMode"
                                                [fields]="fields"
                                                [multiIndex]="m + 1"
                                                (fieldValueChange)="onFieldValueChange($event)">
                                              </app-regular-field>

                                              <div class="multi-buttons">
                                                @if (getMultiArray(nestedField.fieldName, k, parsed.parent + '|' + childGroup, n).controls.length > 1 && !isViewMode && !nestedField.noInput) {
                                                  <button mat-icon-button color="warn" type="button" (click)="removeMultiField(nestedField.fieldName, m, k, parsed.parent + '|' + childGroup, n)" matTooltip="Delete">
                                                    <mat-icon>delete</mat-icon>
                                                  </button>
                                                }

                                                @if (!isViewMode && !nestedField.noInput) {
                                                  <button mat-icon-button color="primary" type="button" (click)="addMultiField(nestedField, k, m, parsed.parent + '|' + childGroup, n)" matTooltip="Add">
                                                    <mat-icon>add</mat-icon>
                                                  </button>
                                                }
                                              </div>
                                            </div>
                                          </div>
                                        }
                                      </div>
                                    }
                                  }

                                  <!-- Nested group control buttons -->
                                  <div class="nested-group-buttons">
                                    @if (!isViewMode) {
                                      <button mat-icon-button color="warn" type="button" (click)="removeNestedGroup(parsed.parent + '|' + childGroup, k, n)" matTooltip="Remove {{ childGroup }} Group" [disabled]="getNestedGroupArray(parsed.parent + '|' + childGroup, k).controls.length <= 1">
                                        <mat-icon>delete</mat-icon>
                                      </button>
                                    }
                                    @if (!isViewMode) {
                                      <button mat-icon-button color="primary" type="button" (click)="addNestedGroup(parsed.parent + '|' + childGroup, k, n)" matTooltip="Add {{ childGroup }} Group">
                                        <mat-icon>add</mat-icon>
                                      </button>
                                    }
                                    @if (!isViewMode) {
                                      <button mat-icon-button color="accent" type="button" (click)="cloneNestedGroup(parsed.parent + '|' + childGroup, k, n)" matTooltip="Clone {{ childGroup }} Group">
                                        <mat-icon>content_copy</mat-icon>
                                      </button>
                                    }
                                  </div>
                                </div>
                              </div>
                            }
                          </div>
                        }

                        <!-- Group control buttons for nested view -->
                        <div class="group-buttons">
                          @if (!isViewMode) {
                            <button mat-icon-button color="warn" type="button" (click)="removeGroup(parsed.parent, k)" matTooltip="Remove Group" [disabled]="getGroupArray(parsed.parent).controls.length <= 1">
                              <mat-icon>delete</mat-icon>
                            </button>
                          }
                          @if (!isViewMode) {
                            <button mat-icon-button color="primary" type="button" (click)="addGroup(parsed.parent, k)" matTooltip="Add Group">
                              <mat-icon>add</mat-icon>
                            </button>
                          }
                          @if (!isViewMode) {
                            <button mat-icon-button color="accent" type="button" (click)="cloneGroup(parsed.parent, k)" matTooltip="Clone Group">
                              <mat-icon>content_copy</mat-icon>
                            </button>
                          }
                        </div>
                      </div>
                    }
                  </div>
                }
              </div>
            }
          }
        }
      </ng-container>
    </div>
  </ng-template>

<!-- Sub-Screen Content Template -->
<ng-template #subScreenContent let-subScreen>
  <!-- ======================================== -->
  <!-- COLUMN ITERATION FOR SUB-SCREEN -->
  <!-- ======================================== -->
  <div class="form-column" *ngFor="let column of subScreenColumns[subScreen.id]">
    
    <!-- ======================================== -->
    <!-- FIELD ITERATION WITHIN COLUMN -->
    <!-- ======================================== -->
    <ng-container *ngFor="let field of column">
    <!-- Skip ID field as it's handled separately -->
    @if (field.fieldName?.toUpperCase() !== 'ID') {

          <!-- ======================================== -->
          <!-- NON-GROUPED FIELDS SECTION -->
          <!-- ======================================== -->
          @if (!field.Group) {

            <!-- ======================================== -->
            <!-- REGULAR FIELD COMPONENT (NON-MULTI) -->
            <!-- ======================================== -->
            @if (!field.isMulti) {
              <app-regular-field
                [field]="field"
                [form]="subScreenForms[subScreen.id]"
                [isViewMode]="isViewMode"
                [fields]="subScreenFields[subScreen.id]"
                (fieldValueChange)="onFieldValueChange($event)">
              </app-regular-field>
            }

            <!-- ======================================== -->
            <!-- MULTI FIELD COMPONENT SECTION -->
            <!-- ======================================== -->
            @if (field.isMulti) {
              <div [formArrayName]="field.fieldName">
                @for (control of getMultiArray(field.fieldName, undefined, undefined, undefined, undefined, subScreen.id).controls; track control; let j = $index) {
                  <div [formGroupName]="j" class="form-field is-multi">
                    <app-regular-field
                      [field]="field"
                      [form]="$any(control)"
                      [isViewMode]="isViewMode"
                      [fields]="subScreenFields[subScreen.id]"
                      [multiIndex]="j + 1"
                      (fieldValueChange)="onFieldValueChange($event)">
                    </app-regular-field>

                    <!-- ======================================== -->
                    <!-- MULTI FIELD ACTION BUTTONS -->
                    <!-- ======================================== -->
                    <div class="multi-buttons">
                      @if (getMultiArray(field.fieldName, undefined, undefined, undefined, undefined, subScreen.id).length > 1 && !isViewMode && !field.noInput) {
                        <button mat-icon-button color="warn" type="button" (click)="removeMultiField(field.fieldName, j, undefined, undefined, undefined, subScreen.id)" matTooltip="Delete">
                          <mat-icon>delete</mat-icon>
                        </button>
                      }

                      @if (!isViewMode && !field.noInput) {
                        <button mat-icon-button color="primary" type="button" (click)="addMultiField(field, undefined, j, undefined, undefined, subScreen.id)" matTooltip="Add">
                          <mat-icon>add</mat-icon>
                        </button>
                      }
                    </div>
                  </div>
                }
              </div>
            }
          }

          <!-- ======================================== -->
          <!-- GROUPED FIELDS COMPONENT SECTION -->
          <!-- ======================================== -->
          @if (field.Group && isFirstFieldInParentGroup(field)) {
            @let parsed = parseGroupPath(field.Group);
            @if (parsed.parent) {
              <div [formArrayName]="parsed.parent" class="grouped-field-section">
                <h3>{{ parsed.parent }}</h3>
                @for (group of getGroupArray(parsed.parent, subScreen.id).controls; track group; let k = $index) {
                  <div [formGroupName]="k" [class]="isRowView ? 'form-grid row-view-table' : 'form-grid multi-field'">

                    @if (isRowView) {
                      <!-- ======================================== -->
                      <!-- ROW VIEW MODE -->
                      <!-- ======================================== -->
                      <div class="row-view-table-container">
                        <!-- Parent group fields -->
                        @for (groupField of getFieldsForGroupFromFields(parsed.parent, subScreenFields[subScreen.id]); track groupField.fieldName) {
                          @if (!groupField.isMulti) {
                            <div class="row-view-table-cell">
                              <app-regular-field
                                [field]="groupField"
                                [form]="$any(group)"
                                [isViewMode]="isViewMode"
                                [fields]="subScreenFields[subScreen.id]"
                                (fieldValueChange)="onFieldValueChange($event)">
                              </app-regular-field>
                            </div>
                          }

                          <!-- Multi-fields for parent group -->
                          @if (groupField.isMulti) {
                            <div class="row-view-table-cell-multi" [formArrayName]="groupField.fieldName">
                              <label>{{ groupField.fieldName }}</label>
                              @for (multiControl of getMultiArray(groupField.fieldName, k, field.Group, undefined, undefined, subScreen.id).controls; track multiControl; let l = $index) {
                                <div [formGroupName]="l" class="row-view-multi-item">
                                  <div class="row-view-multi-input">
                                    <app-regular-field
                                      [field]="groupField"
                                      [form]="$any(multiControl)"
                                      [isViewMode]="isViewMode"
                                      [fields]="subScreenFields[subScreen.id]"
                                      [multiIndex]="l + 1"
                                      (fieldValueChange)="onFieldValueChange($event)">
                                    </app-regular-field>
                                  </div>
                                  <div class="row-view-multi-buttons">
                                    @if (getMultiArray(groupField.fieldName, k, field.Group, undefined, undefined, subScreen.id).controls.length > 1 && !isViewMode && !groupField.noInput) {
                                      <button mat-icon-button color="warn" type="button" (click)="removeMultiField(groupField.fieldName, l, k, field.Group, undefined, subScreen.id)" matTooltip="Delete">
                                        <mat-icon>delete</mat-icon>
                                      </button>
                                    }
                                    @if (!isViewMode && !groupField.noInput) {
                                      <button mat-icon-button color="primary" type="button" (click)="addMultiField(groupField, k, l, field.Group, undefined, subScreen.id)" matTooltip="Add">
                                        <mat-icon>add</mat-icon>
                                      </button>
                                    }
                                  </div>
                                </div>
                              }
                            </div>
                          }
                        }

                        <!-- ======================================== -->
                        <!-- NESTED GROUP FIELDS (ROW VIEW) -->
                        <!-- ======================================== -->
                        @for (childGroup of getChildGroupsFromFields(parsed.parent, subScreenFields[subScreen.id]); track childGroup) {
                          @for (nestedGroup of getNestedGroupArray(parsed.parent + '|' + childGroup, k, subScreen.id).controls; track nestedGroup; let n = $index) {
                            @for (nestedField of getFieldsForGroupPathFromFields(parsed.parent + '|' + childGroup, subScreenFields[subScreen.id]); track nestedField.fieldName) {
                              @if (!nestedField.isMulti) {
                                <div class="row-view-table-cell" [formArrayName]="childGroup" [formGroupName]="n">
                                  <app-regular-field
                                    [field]="nestedField"
                                    [form]="$any(nestedGroup)"
                                    [isViewMode]="isViewMode"
                                    [fields]="subScreenFields[subScreen.id]"
                                    (fieldValueChange)="onFieldValueChange($event)">
                                  </app-regular-field>
                                </div>
                              }

                              <!-- Multi-fields for nested groups -->
                              @if (nestedField.isMulti) {
                                <div class="row-view-table-cell-multi" [formArrayName]="childGroup" [formGroupName]="n">
                                  <div [formArrayName]="nestedField.fieldName">
                                    <label>{{ nestedField.fieldName }}</label>
                                    @for (multiControl of getMultiArray(nestedField.fieldName, k, parsed.parent + '|' + childGroup, n, undefined, subScreen.id).controls; track multiControl; let m = $index) {
                                      <div [formGroupName]="m" class="row-view-multi-item">
                                        <div class="row-view-multi-input">
                                          <app-regular-field
                                            [field]="nestedField"
                                            [form]="$any(multiControl)"
                                            [isViewMode]="isViewMode"
                                            [fields]="subScreenFields[subScreen.id]"
                                            [multiIndex]="m + 1"
                                            (fieldValueChange)="onFieldValueChange($event)">
                                          </app-regular-field>
                                        </div>
                                        <div class="row-view-multi-buttons">
                                          @if (getMultiArray(nestedField.fieldName, k, parsed.parent + '|' + childGroup, n, undefined, subScreen.id).controls.length > 1 && !isViewMode && !nestedField.noInput) {
                                            <button mat-icon-button color="warn" type="button" (click)="removeMultiField(nestedField.fieldName, m, k, parsed.parent + '|' + childGroup, n, subScreen.id)" matTooltip="Delete">
                                              <mat-icon>delete</mat-icon>
                                            </button>
                                          }
                                          @if (!isViewMode && !nestedField.noInput) {
                                            <button mat-icon-button color="primary" type="button" (click)="addMultiField(nestedField, k, m, parsed.parent + '|' + childGroup, n, subScreen.id)" matTooltip="Add">
                                              <mat-icon>add</mat-icon>
                                            </button>
                                          }
                                        </div>
                                      </div>
                                    }
                                  </div>
                                </div>
                              }
                            }

                            <!-- Nested group control buttons -->
                            <div class="row-view-table-actions" [formArrayName]="childGroup" [formGroupName]="n">
                              @if (!isViewMode) {
                                <button mat-icon-button color="warn" type="button" (click)="removeNestedGroup(parsed.parent + '|' + childGroup, k, n, subScreen.id)" matTooltip="Remove {{ childGroup }} Group" [disabled]="getNestedGroupArray(parsed.parent + '|' + childGroup, k, subScreen.id).controls.length <= 1">
                                  <mat-icon>delete</mat-icon>
                                </button>
                              }
                              @if (!isViewMode) {
                                <button mat-icon-button color="primary" type="button" (click)="addNestedGroup(parsed.parent + '|' + childGroup, k, n, subScreen.id)" matTooltip="Add {{ childGroup }} Group">
                                  <mat-icon>add</mat-icon>
                                </button>
                              }
                              @if (!isViewMode) {
                                <button mat-icon-button color="accent" type="button" (click)="cloneNestedGroup(parsed.parent + '|' + childGroup, k, n, subScreen.id)" matTooltip="Clone {{ childGroup }} Group">
                                  <mat-icon>content_copy</mat-icon>
                                </button>
                              }
                            </div>
                          }
                        }

                        <!-- Main group action buttons -->
                        <div class="row-view-table-actions">
                          @if (!isViewMode) {
                            <button mat-icon-button color="warn" type="button" (click)="removeGroup(parsed.parent, k, subScreen.id)" matTooltip="Remove Group" [disabled]="getGroupArray(parsed.parent, subScreen.id).controls.length <= 1">
                              <mat-icon>delete</mat-icon>
                            </button>
                            <button mat-icon-button color="primary" type="button" (click)="addGroup(parsed.parent, k, subScreen.id)" matTooltip="Add Group">
                              <mat-icon>add</mat-icon>
                            </button>
                            <button mat-icon-button color="accent" type="button" (click)="cloneGroup(parsed.parent, k, subScreen.id)" matTooltip="Clone Group">
                              <mat-icon>content_copy</mat-icon>
                            </button>
                          }
                        </div>
                      </div>
                    } @else {
                      <!-- Nested View: Original layout -->
                      <div [class]="isRowView ? 'group-fields row-view-fields' : 'group-fields'">

                        <!-- Direct fields of parent group -->
                        @for (groupField of getFieldsForGroupFromFields(parsed.parent, subScreenFields[subScreen.id]); track groupField.fieldName) {

                          @if (!groupField.isMulti) {
                            <app-regular-field
                              [field]="groupField"
                              [form]="$any(group)"
                              [isViewMode]="isViewMode"
                              [fields]="subScreenFields[subScreen.id]"
                              (fieldValueChange)="onFieldValueChange($event)">
                            </app-regular-field>
                          }

                          @if (groupField.isMulti) {
                            <div [formArrayName]="groupField.fieldName">
                              <h4>{{ groupField.fieldName }}</h4>

                              @for (multiControl of getMultiArray(groupField.fieldName, k, field.Group, undefined, undefined, subScreen.id).controls; track multiControl; let l = $index) {
                                <div [formGroupName]="l" class="form-field">
                                  <app-regular-field
                                    [field]="groupField"
                                    [form]="$any(multiControl)"
                                    [isViewMode]="isViewMode"
                                    [fields]="subScreenFields[subScreen.id]"
                                    [multiIndex]="l + 1"
                                    (fieldValueChange)="onFieldValueChange($event)">
                                  </app-regular-field>

                                  @if (getMultiArray(groupField.fieldName, k, field.Group, undefined, undefined, subScreen.id).controls.length > 1 && !isViewMode && !groupField.noInput) {
                                    <button mat-icon-button color="warn" type="button" (click)="removeMultiField(groupField.fieldName, l, k, field.Group, undefined, subScreen.id)" matTooltip="Delete">
                                      <mat-icon>delete</mat-icon>
                                    </button>
                                  }

                                  @if (!isViewMode && !groupField.noInput) {
                                    <button mat-icon-button color="primary" type="button" (click)="addMultiField(groupField, k, l, field.Group, undefined, subScreen.id)" matTooltip="Add">
                                      <mat-icon>add</mat-icon>
                                    </button>
                                  }
                                </div>
                              }
                            </div>
                          }
                        }

                        <!-- Nested subgroups -->
                        @for (childGroup of getChildGroupsFromFields(parsed.parent, subScreenFields[subScreen.id]); track childGroup) {
                          <div [formArrayName]="childGroup" [class]="isRowView ? 'nested-group-section row-view-nested-seamless' : 'nested-group-section'">
                            <h4 [class.row-view-hidden]="isRowView">{{ childGroup }}</h4>
                            @for (nestedGroup of getNestedGroupArray(parsed.parent + '|' + childGroup, k, subScreen.id).controls; track nestedGroup; let n = $index) {
                              <div [formGroupName]="n" [class]="isRowView ? 'form-grid nested-field row-view-nested-field-seamless' : 'form-grid nested-field'">
                                <div [class]="isRowView ? 'nested-group-fields row-view-nested-fields-seamless' : 'nested-group-fields'">
                                  @for (nestedField of getFieldsForGroupPathFromFields(parsed.parent + '|' + childGroup, subScreenFields[subScreen.id]); track nestedField.fieldName) {

                                    @if (!nestedField.isMulti) {
                                      <app-regular-field
                                        [field]="nestedField"
                                        [form]="$any(nestedGroup)"
                                        [isViewMode]="isViewMode"
                                        [fields]="subScreenFields[subScreen.id]"
                                        (fieldValueChange)="onFieldValueChange($event)">
                                      </app-regular-field>
                                    }

                                    @if (nestedField.isMulti) {
                                      <div [formArrayName]="nestedField.fieldName">
                                        <h5>{{ nestedField.fieldName }}</h5>

                                        @for (multiControl of getMultiArray(nestedField.fieldName, k, parsed.parent + '|' + childGroup, n, undefined, subScreen.id).controls; track multiControl; let m = $index) {
                                          <div [formGroupName]="m" class="form-field is-multi">
                                            <div class="multi-input-container">
                                              <app-regular-field
                                                [field]="nestedField"
                                                [form]="$any(multiControl)"
                                                [isViewMode]="isViewMode"
                                                [fields]="subScreenFields[subScreen.id]"
                                                [multiIndex]="m + 1"
                                                (fieldValueChange)="onFieldValueChange($event)">
                                              </app-regular-field>

                                              <div class="multi-buttons">
                                                @if (getMultiArray(nestedField.fieldName, k, parsed.parent + '|' + childGroup, n, undefined, subScreen.id).controls.length > 1 && !isViewMode && !nestedField.noInput) {
                                                  <button mat-icon-button color="warn" type="button" (click)="removeMultiField(nestedField.fieldName, m, k, parsed.parent + '|' + childGroup, n, subScreen.id)" matTooltip="Delete">
                                                    <mat-icon>delete</mat-icon>
                                                  </button>
                                                }

                                                @if (!isViewMode && !nestedField.noInput) {
                                                  <button mat-icon-button color="primary" type="button" (click)="addMultiField(nestedField, k, m, parsed.parent + '|' + childGroup, n, subScreen.id)" matTooltip="Add">
                                                    <mat-icon>add</mat-icon>
                                                  </button>
                                                }
                                              </div>
                                            </div>
                                          </div>
                                        }
                                      </div>
                                    }
                                  }

                                  <!-- Nested group control buttons -->
                                  <div class="nested-group-buttons">
                                    @if (!isViewMode) {
                                      <button mat-icon-button color="warn" type="button" (click)="removeNestedGroup(parsed.parent + '|' + childGroup, k, n, subScreen.id)" matTooltip="Remove {{ childGroup }} Group" [disabled]="getNestedGroupArray(parsed.parent + '|' + childGroup, k, subScreen.id).controls.length <= 1">
                                        <mat-icon>delete</mat-icon>
                                      </button>
                                    }
                                    @if (!isViewMode) {
                                      <button mat-icon-button color="primary" type="button" (click)="addNestedGroup(parsed.parent + '|' + childGroup, k, n, subScreen.id)" matTooltip="Add {{ childGroup }} Group">
                                        <mat-icon>add</mat-icon>
                                      </button>
                                    }
                                    @if (!isViewMode) {
                                      <button mat-icon-button color="accent" type="button" (click)="cloneNestedGroup(parsed.parent + '|' + childGroup, k, n, subScreen.id)" matTooltip="Clone {{ childGroup }} Group">
                                        <mat-icon>content_copy</mat-icon>
                                      </button>
                                    }
                                  </div>
                                </div>
                              </div>
                            }
                          </div>
                        }

                        <!-- Group control buttons for nested view -->
                        <div class="group-buttons">
                          @if (!isViewMode) {
                            <button mat-icon-button color="warn" type="button" (click)="removeGroup(parsed.parent, k, subScreen.id)" matTooltip="Remove Group" [disabled]="getGroupArray(parsed.parent, subScreen.id).controls.length <= 1">
                              <mat-icon>delete</mat-icon>
                            </button>
                          }
                          @if (!isViewMode) {
                            <button mat-icon-button color="primary" type="button" (click)="addGroup(parsed.parent, k, subScreen.id)" matTooltip="Add Group">
                              <mat-icon>add</mat-icon>
                            </button>
                          }
                          @if (!isViewMode) {
                            <button mat-icon-button color="accent" type="button" (click)="cloneGroup(parsed.parent, k, subScreen.id)" matTooltip="Clone Group">
                              <mat-icon>content_copy</mat-icon>
                            </button>
                          }
                        </div>
                      </div>
                    }
                  </div>
                }
              </div>
            }
          }
        }
      </ng-container>
    </div>
  </ng-template>
