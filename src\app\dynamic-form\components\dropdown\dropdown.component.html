<!-- 
  UNIFIED DROPDOWN COMPONENT TEMPLATE
  
  This template provides a comprehensive dropdown interface with the following features:
  - Searchable input field with form control integration
  - Toggleable dropdown list with filtered options
  - Loading states and empty states
  - Different display modes for various dropdown types
  - Accessibility features and keyboard navigation
  - Responsive design with Material Design components
  
  STRUCTURE:
  - Input container with relative positioning
  - Search input field with event handlers
  - Arrow button for dropdown toggle
  - Dropdown list with conditional rendering
  - Loading, options, and empty states
  
  DROPDOWN TYPES:
  - ID dropdowns: Display ID property
  - Type/Foreign Key dropdowns: Display ROW_ID only
  - Regular dropdowns: Display all properties
  
  ACCESSIBILITY:
  - Proper ARIA labels and roles
  - Keyboard navigation support
  - Focus management
  - Screen reader compatibility
-->

<!-- Main dropdown container with disabled state support -->
<div class="dropdown-input-container" [class.disabled]="isDisabled || isReadonly">
  
  <!-- 
    SEARCH INPUT FIELD
    - Integrated with Angular Reactive Forms
    - Supports autocomplete off for custom dropdown behavior
    - Handles input changes with debounced search
    - Manages focus and blur events for dropdown toggle
    - Supports disabled and readonly states
  -->
  <input 
    [id]="inputId || fieldName"
    [formControl]="formControl"
    [class]="inputClass"
    [placeholder]="placeholderText"
    [disabled]="isDisabled"
    [readonly]="isReadonly"
    type="text"
    autocomplete="off"
    (input)="onInputChange($event)"
    (focus)="onInputFocus()"
    (blur)="onInputBlur()" />
  
  <!-- 
    DROPDOWN ARROW BUTTON
    - Toggles dropdown visibility
    - Shows loading state with rotating icon
    - Disabled when input is disabled/readonly
    - Material Design icon with tooltip
    - Positioned absolutely on the right side
  -->
  @if (showArrowButton) {
    <button 
      type="button" 
      class="dropdown-arrow-btn" 
      (click)="toggleDropdown()" 
      [disabled]="isDisabled || isReadonly"
      [matTooltip]="tooltipText">
      <mat-icon>{{ dropdownArrowIcon }}</mat-icon>
    </button>
  }
  
  <!-- 
    DROPDOWN LIST CONTAINER
    - Conditionally rendered based on showDropdown state
    - Positioned absolutely below input
    - Configurable max height for scrolling
    - Z-index management for proper layering
  -->
  @if (showDropdown) {
    <div class="dropdown-list" [style.max-height]="dropdownMaxHeight">
      
      <!-- 
        LOADING STATE
        - Shown during API calls or data fetching
        - Animated spinner icon
        - Centered layout with gap
        - Consistent styling with other states
      -->
      @if (isLoading) {
        <div class="dropdown-loading">
          <mat-icon>refresh</mat-icon>
          Loading...
        </div>
      }
      
      <!-- 
        OPTIONS LIST
        - Rendered when filtered options are available
        - TrackBy function for performance optimization
        - Click handlers for option selection
        - Different display modes based on dropdown type
      -->
      @else if (filteredOptions && filteredOptions.length > 0) {
        @for (option of filteredOptions; track trackByOptionId($index, option)) {
          <div class="dropdown-item" (click)="selectOption(option)">
            
            <!-- 
              ID DROPDOWN DISPLAY
              - Shows ID property for ID-type dropdowns
              - Used for record identification
              - Primary display for ID selection
            -->
            @if (config.type === 'id') {
              {{ option['ID'] }}
            }
            
            <!-- 
              TYPE AND FOREIGN KEY DROPDOWN DISPLAY
              - Shows only ROW_ID for type and foreign key dropdowns
              - Simplified display for better UX
              - Used for field type and relationship selection
            -->
            @else if (config.type === 'type' || config.type === 'foreignKey') {
              {{ option.ROW_ID }}
            }
            
            <!-- 
              REGULAR DROPDOWN DISPLAY
              - Shows all properties for regular dropdowns
              - Iterates through all keys in option object
              - Used for complex data display
              - Space-separated values for readability
            -->
            @else {
              @for (key of getKeys(option); track trackByKey($index, key)) {
                {{ option[key] }}&nbsp;
              }
            }
          </div>
        }
      }
      
      <!-- 
        EMPTY STATE
        - Shown when no options are available
        - Configurable message from component
        - Centered text with italic styling
        - Provides user feedback for empty results
      -->
      @else {
        <div class="dropdown-empty">
          {{ emptyMessage }}
        </div>
      }
    </div>
  }
</div>
