<!-- 
  INITIAL INPUT COMPONENT TEMPLATE
  
  This template provides the initial input interface for dynamic forms, typically used
  for ID selection before loading the full form. It includes an ID dropdown and action
  buttons for different operations.
  
  STRUCTURE:
  - Form container with reactive form integration
  - ID input field with unified dropdown component
  - Action buttons for Add, Edit, View, and Maintenance operations
  - Responsive layout with proper spacing and alignment
  
  FEATURES:
  - ID dropdown with search functionality
  - Four action buttons with distinct color themes
  - Material Design icons and tooltips
  - Responsive design for different screen sizes
  - Form validation integration
  
  BUTTON THEMES:
  - Add: Green theme for creating new records
  - Edit: Blue theme for modifying existing records
  - View: Orange theme for viewing record details
  - Maintenance: Red theme for maintenance operations
  
  ACCESSIBILITY:
  - Proper form labels and associations
  - Tooltips for button actions
  - Keyboard navigation support
  - Screen reader compatibility
-->

<!-- Main container for initial input interface -->
<div class="initial-input">
  
  <!-- 
    FORM CONTAINER
    - Angular Reactive Form integration
    - Flexbox layout for responsive design
    - Proper spacing and background styling
  -->
  <form [formGroup]="form" class="form-container">
    
    <!-- 
      MAIN FIELD SECTION
      - Contains ID input and action buttons
      - Horizontal layout with proper alignment
      - Responsive design with flexbox
    -->
    <div class="form-main-field">
      
      <!-- 
        FORM LABEL
        - Clear label for ID field
        - Consistent typography with design system
        - Proper spacing and alignment
      -->
      <label for="ID" class="form-label">ID</label>
      
      <!-- 
        INPUT AND BUTTON GROUP
        - Contains ID dropdown and action buttons
        - Flexbox layout for responsive alignment
        - Proper spacing and wrapping behavior
      -->
      <div class="input-button-group">
        
        <!-- 
          ID INPUT CONTAINER
          - Wrapper for the unified dropdown component
          - Ensures proper width and responsive behavior
          - Integrates with form validation
        -->
        <div class="id-input-container">
          <app-dropdown
            fieldName="ID"
            [formControl]="getFormControl('ID')"
            [config]="getIdDropdownConfig()"
            [isDisabled]="false"
            [isReadonly]="false"
            inputId="ID"
            [cssClass]="getIdInputClass()"
            (valueChange)="onDropdownValueChange($event)"
            (searchChange)="onSearchChange($event)">
          </app-dropdown>
        </div>

        <!-- 
          BUTTON CONTAINER
          - Groups action buttons with rounded corners
          - Light background with subtle shadow
          - Proper spacing and responsive behavior
        -->
        <div class="button-container">
          
          <!-- 
            ADD BUTTON
            - Green theme for creating new records
            - Material Design add icon
            - Tooltip for accessibility
            - Hover effects for better UX
          -->
          <button mat-raised-button color="primary" type="button" 
                  (click)="onAddClick()" 
                  matTooltip="Add"
                  class="initial-input-button add-button">
            <mat-icon>add</mat-icon>
          </button>

          <!-- 
            EDIT BUTTON
            - Blue theme for modifying existing records
            - Material Design edit icon
            - Tooltip for accessibility
            - Consistent styling with other buttons
          -->
          <button mat-raised-button color="primary" type="button" 
                  (click)="onEditClick()" 
                  matTooltip="Edit"
                  class="initial-input-button edit-button">
            <mat-icon>edit</mat-icon>
          </button>

          <!-- 
            VIEW BUTTON
            - Orange theme for viewing record details
            - Material Design visibility icon
            - Tooltip for accessibility
            - Distinct color for clear action identification
          -->
          <button mat-raised-button color="accent" type="button" 
                  (click)="onViewClick()" 
                  matTooltip="View"
                  class="initial-input-button view-button">
            <mat-icon>visibility</mat-icon>
          </button>

          <!-- 
            MAINTENANCE BUTTON
            - Red theme for maintenance operations
            - Material Design build icon
            - Tooltip for accessibility
            - Warning color for maintenance actions
          -->
          <button mat-raised-button color="warn" type="button" 
                  (click)="onMaintenanceClick()"
                  matTooltip="Maintenance"
                  class="initial-input-button maintenance-button">
            <mat-icon>build</mat-icon>
          </button>
        </div>
      </div>
    </div>
  </form>
</div>
