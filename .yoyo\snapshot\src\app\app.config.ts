import { ApplicationConfig, provideZoneChangeDetection } from '@angular/core';
import { provideRouter, withPreloading, PreloadAllModules, withViewTransitions } from '@angular/router';
import { provideHttpClient , withInterceptors} from '@angular/common/http';
import { provideAnimationsAsync } from '@angular/platform-browser/animations/async';
import { privilegesInterceptor } from './interceptors/privileges.interceptor';



import { routes } from './app.routes';

export const appConfig: ApplicationConfig = {
  providers: [
    provideZoneChangeDetection({ eventCoalescing: true }),
    provideRouter(routes,
      withPreloading(PreloadAllModules)
   ,withViewTransitions() ),
    provideHttpClient(withInterceptors([privilegesInterceptor])), // this is used to make http request this should define before making any http request
    provideAnimationsAsync() // Enable Angular Material animations
  ]
};
