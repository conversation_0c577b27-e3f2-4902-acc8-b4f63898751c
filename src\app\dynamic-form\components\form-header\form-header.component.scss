/**
 * FORM HEADER COMPONENT STYLES
 * 
 * This SCSS file provides comprehensive styling for the form header component
 * with support for action buttons, responsive design, and visual feedback.
 * 
 * DESIGN SYSTEM:
 * - Uses Poppins font family for consistency
 * - Material Design color palette for buttons
 * - Gradient backgrounds for modern appearance
 * - Consistent spacing and border radius
 * - Smooth transitions for better UX
 * 
 * LAYOUT STRUCTURE:
 * - Horizontal container with flexbox layout
 * - ID display field on the left
 * - Action buttons grouped on the right
 * - Responsive design with mobile breakpoints
 * - Error message integration
 * 
 * BUTTON THEMES:
 * - Toggle View: Teal (#009688) for view switching
 * - Submit: Green (#4CAF50) for form submission
 * - Validate: Blue (#2196F3) for validation
 * - Authorize: Purple (#9C27B0) for authorization
 * - Back: Gray (#607D8B) for navigation
 * - Reject: Red (#F44336) for rejection
 * - Delete: Dark Red (#D32F2F) for deletion
 * 
 * RESPONSIVE DESIGN:
 * - Desktop-first approach with mobile breakpoints
 * - Breakpoints at 768px and 480px
 * - Stacked layout on mobile devices
 * - Touch-friendly button sizes
 */

@use '../../../../styles/shared.scss' as *;

/* ==================== MAIN CONTAINER ==================== */

/**
 * FORM HEADER
 * 
 * Main wrapper for the form header:
 * - White background for clean appearance
 * - Proper padding for visual spacing
 * - Full width utilization
 */
.form-header {
  background-color: white;
  padding: 16px 0;
}

/* ==================== HORIZONTAL LAYOUT ==================== */

/**
 * HORIZONTAL CONTAINER
 * 
 * Flexbox layout for responsive alignment:
 * - Space-between for proper distribution
 * - Responsive gap and padding
 * - Clean white background
 * - Aligns items center for proper alignment
 */
.horizontal-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 24px;
  padding: 0 24px;
  background-color: white;
}

/* ==================== FORM FIELD SECTION ==================== */

/**
 * FORM FIELD
 * 
 * Container for the ID display:
 * - Flexbox alignment for proper positioning
 * - Responsive behavior for different screen sizes
 */
.form-field {
  display: flex;
  align-items: center;
  
  /**
   * ID DISPLAY PARAGRAPH
   * 
   * Styling for the ID display element:
   * - Poppins font with semi-bold weight
   * - Brand color for visual emphasis
   * - Badge-like appearance with background
   * - Rounded corners for modern look
   * - Minimum width for consistent sizing
   * - Centered text alignment
   */
  p {
    font-family: 'Poppins', sans-serif;
    font-weight: 600;
    font-size: 18px;
    color: #283A97;
    margin: 0;
    padding: 8px 16px;
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    min-width: 120px;
    text-align: center;
  }
}

/* ==================== BUTTON GROUP SECTION ==================== */

/**
 * BUTTON GROUP
 * 
 * Container for action buttons and error message:
 * - Flexbox layout with proper spacing
 * - Responsive wrapping behavior
 * - Gap spacing for visual separation
 */
.button-group {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

/* ==================== BUTTON CONTAINER ==================== */

/**
 * BUTTON CONTAINER
 * 
 * Groups action buttons with rounded corners:
 * - Flexbox layout for button alignment
 * - Light background with subtle shadow
 * - Rounded corners for modern appearance
 * - Horizontal scrolling for overflow
 * - Proper spacing and responsive behavior
 * - Hidden scrollbars for clean appearance
 */
.button-container {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background-color: #f8f9fa;
  border-radius: 12px;
  border: 1px solid #e9ecef;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  flex-wrap: nowrap;
  overflow-x: auto;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
  
  &::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
  }
}

/* ==================== FORM ACTION BUTTON STYLES ==================== */

/**
 * ENHANCED MAIN FORM ACTION BUTTON STYLES
 * 
 * Base styling for all action buttons:
 * - Flexbox layout with proper alignment
 * - Consistent padding and border radius
 * - Poppins font for typography consistency
 * - Smooth transitions for hover effects
 * - Box shadow for depth and emphasis
 * - Flex-shrink prevention for consistent sizing
 * - White-space nowrap for text preservation
 */
.form-action-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  padding: 6px 12px;
  border: 2px solid;
  border-radius: 8px;
  font-family: 'Poppins', sans-serif;
  font-weight: 500;
  font-size: 13px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  flex-shrink: 0;
  white-space: nowrap;
  
  /**
   * BUTTON ICON STYLING
   * 
   * Material Design icon styling:
   * - 12px size for optimal visibility
   * - Flexbox alignment for centering
   * - Proper line height and spacing
   * - Consistent sizing across buttons
   */
  mat-icon {
    font-size: 12px;
    width: 12px;
    height: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    line-height: 1;
    margin: 0;
    padding: 0;
  }
}

/* ==================== BUTTON THEMES ==================== */

/**
 * TOGGLE VIEW BUTTON - TEAL THEME
 * 
 * Styling for the view toggle button:
 * - Teal gradient background for modern appearance
 * - White text for contrast
 * - Hover effects with darker gradient
 * - Transform and shadow effects for interactivity
 * - Used for switching between view modes
 */
.form-action-button.toggle-view-button {
  border-color: #009688;
  background: linear-gradient(135deg, #009688 0%, #00796B 100%);
  color: white;
  
  &:hover {
    background: linear-gradient(135deg, #00796B 0%, #00695C 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 150, 136, 0.4);
  }
  
  &:active {
    transform: translateY(0);
    box-shadow: 0 2px 6px rgba(0, 150, 136, 0.3);
  }
}

/**
 * SUBMIT BUTTON - GREEN THEME
 * 
 * Styling for the submit button:
 * - Green gradient background for positive action
 * - White text for contrast
 * - Hover effects with darker gradient
 * - Disabled state with reduced opacity
 * - Used for form submission
 */
.form-action-button.submit-button {
  border-color: #4CAF50;
  background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
  color: white;
  
  &:hover {
    background: linear-gradient(135deg, #45a049 0%, #3d8b40 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(76, 175, 80, 0.4);
  }
  
  &:active {
    transform: translateY(0);
    box-shadow: 0 2px 6px rgba(76, 175, 80, 0.3);
  }
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
  }
}

/**
 * VALIDATE BUTTON - BLUE THEME
 * 
 * Styling for the validate button:
 * - Blue gradient background for validation action
 * - White text for contrast
 * - Hover effects with darker gradient
 * - Transform and shadow effects for interactivity
 * - Used for form validation
 */
.form-action-button.validate-button {
  border-color: #2196F3;
  background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
  color: white;
  
  &:hover {
    background: linear-gradient(135deg, #1976D2 0%, #1565C0 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(33, 150, 243, 0.4);
  }
  
  &:active {
    transform: translateY(0);
    box-shadow: 0 2px 6px rgba(33, 150, 243, 0.3);
  }
}

/**
 * AUTHORIZE BUTTON - PURPLE THEME
 * 
 * Styling for the authorize button:
 * - Purple gradient background for authorization
 * - White text for contrast
 * - Hover effects with darker gradient
 * - Transform and shadow effects for interactivity
 * - Used for record authorization
 */
.form-action-button.authorize-button {
  border-color: #9C27B0;
  background: linear-gradient(135deg, #9C27B0 0%, #7B1FA2 100%);
  color: white;
  
  &:hover {
    background: linear-gradient(135deg, #7B1FA2 0%, #6A1B9A 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(156, 39, 176, 0.4);
  }
  
  &:active {
    transform: translateY(0);
    box-shadow: 0 2px 6px rgba(156, 39, 176, 0.3);
  }
}

/**
 * BACK BUTTON - GRAY THEME
 * 
 * Styling for the back button:
 * - Gray gradient background for navigation
 * - White text for contrast
 * - Hover effects with darker gradient
 * - Transform and shadow effects for interactivity
 * - Used for navigation back
 */
.form-action-button.back-button {
  border-color: #607D8B;
  background: linear-gradient(135deg, #607D8B 0%, #455A64 100%);
  color: white;
  
  &:hover {
    background: linear-gradient(135deg, #455A64 0%, #37474F 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(96, 125, 139, 0.4);
  }
  
  &:active {
    transform: translateY(0);
    box-shadow: 0 2px 6px rgba(96, 125, 139, 0.3);
  }
}

/**
 * REJECT BUTTON - RED THEME
 * 
 * Styling for the reject button:
 * - Red gradient background for rejection action
 * - White text for contrast
 * - Hover effects with darker gradient
 * - Transform and shadow effects for interactivity
 * - Used for record rejection
 */
.form-action-button.reject-button {
  border-color: #F44336;
  background: linear-gradient(135deg, #F44336 0%, #D32F2F 100%);
  color: white;
  
  &:hover {
    background: linear-gradient(135deg, #D32F2F 0%, #C62828 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(244, 67, 54, 0.4);
  }
  
  &:active {
    transform: translateY(0);
    box-shadow: 0 2px 6px rgba(244, 67, 54, 0.3);
  }
}

/**
 * DELETE BUTTON - DARK RED THEME
 * 
 * Styling for the delete button:
 * - Dark red gradient background for destructive action
 * - White text for contrast
 * - Hover effects with darker gradient
 * - Transform and shadow effects for interactivity
 * - Used for record deletion
 */
.form-action-button.delete-button {
  border-color: #D32F2F;
  background: linear-gradient(135deg, #D32F2F 0%, #B71C1C 100%);
  color: white;
  
  &:hover {
    background: linear-gradient(135deg, #B71C1C 0%, #8E0000 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(211, 47, 47, 0.4);
  }
  
  &:active {
    transform: translateY(0);
    box-shadow: 0 2px 6px rgba(211, 47, 47, 0.3);
  }
}

/* ==================== ERROR MESSAGE STYLING ==================== */

/**
 * ERROR MESSAGE
 * 
 * Styling for error message display:
 * - Red color for error indication
 * - Light red background for subtle emphasis
 * - Border for visual definition
 * - Rounded corners for modern appearance
 * - Proper spacing and alignment
 * - Responsive text sizing
 */
.error-message {
  color: #f44336;
  font-size: 14px;
  font-weight: 500;
  padding: 8px 12px;
  background-color: rgba(244, 67, 54, 0.1);
  border: 1px solid rgba(244, 67, 54, 0.3);
  border-radius: 4px;
  margin-left: 12px;
}

/* ==================== RESPONSIVE DESIGN ==================== */

/**
 * TABLET RESPONSIVE STYLING (max-width: 768px)
 * 
 * Adjustments for tablet-sized screens:
 * - Stacked layout for better mobile experience
 * - Centered alignment for visual balance
 * - Reduced spacing and padding
 * - Maintained touch-friendly button sizes
 * - Responsive text and icon sizing
 */
@media (max-width: 768px) {
  .horizontal-container {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
    padding: 0 16px;
  }
  
  .form-field {
    justify-content: center;
    
    p {
      font-size: 16px;
      min-width: 100px;
    }
  }
  
  .button-group {
    justify-content: center;
    gap: 8px;
  }
  
  .button-container {
    padding: 6px 8px;
    gap: 6px;
    justify-content: center;
  }
  
  .form-action-button {
    padding: 5px 10px;
    font-size: 12px;
    
    mat-icon {
      font-size: 8px;
      width: 8px;
      height: 8px;
    }
  }
}

/**
 * MOBILE RESPONSIVE STYLING (max-width: 480px)
 * 
 * Adjustments for mobile-sized screens:
 * - Minimal padding and spacing
 * - Compact layout for small screens
 * - Reduced button sizes for space efficiency
 * - Maintained usability on small devices
 * - Responsive error message styling
 */
@media (max-width: 480px) {
  .button-group {
    gap: 6px;
  }
  
  .button-container {
    padding: 4px 6px;
    gap: 4px;
  }
  
  .form-action-button {
    padding: 4px 8px;
    font-size: 11px;
    
    mat-icon {
      font-size: 7px;
      width: 7px;
      height: 7px;
    }
  }
  
  .error-message {
    font-size: 12px;
    padding: 6px 8px;
    margin-left: 8px;
  }
}

/* ==================== READONLY STATE ==================== */

/**
 * READONLY BUTTON STATE
 * 
 * Styling for disabled/readonly buttons:
 * - Reduced opacity for visual indication
 * - Disabled cursor for clear interaction state
 * - Disabled pointer events for proper behavior
 * - Used when buttons should not be interactive
 */
.readonly-button {
  pointer-events: none;
  opacity: 0.6;
  cursor: not-allowed;
}