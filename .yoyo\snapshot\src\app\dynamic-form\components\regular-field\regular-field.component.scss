/**
 * REGULAR FIELD COMPONENT STYLES
 * 
 * This SCSS file provides comprehensive styling for regular form fields in dynamic forms
 * with support for various input types, responsive design, and accessibility features.
 * 
 * DESIGN SYSTEM:
 * - Uses consistent spacing and typography
 * - Material Design color palette
 * - Consistent border radius (8px) and spacing
 * - Smooth transitions for better UX
 * - Proper focus and hover states
 * 
 * LAYOUT STRUCTURE:
 * - Form field container with full width
 * - Label positioning and styling
 * - Input field styling for various types
 * - Responsive design with mobile breakpoints
 * - Disabled/readonly state management
 * 
 * INPUT TYPES SUPPORTED:
 * - Text inputs with placeholder support
 * - Number inputs for integers and decimals
 * - Date inputs with native picker
 * - Checkbox inputs with custom styling
 * - Dropdown integration for foreign keys
 * 
 * RESPONSIVE DESIGN:
 * - Desktop-first approach with mobile breakpoints
 * - Breakpoints at 768px and 480px
 * - Touch-friendly sizing on mobile
 * - iOS zoom prevention for text inputs
 */

/* ==================== FORM FIELD CONTAINER ==================== */

/**
 * FORM FIELD CONTAINER
 * 
 * Main wrapper for form fields:
 * - Full width utilization
 * - Proper margin for spacing
 * - Relative positioning for child elements
 * - Consistent layout across field types
 */
.form-field {
  width: 100%;
  margin-bottom: 16px;
  position: relative;
}

/* ==================== FIELD LABEL STYLING ==================== */

/**
 * FIELD LABEL
 * 
 * Styling for field labels:
 * - Block display for proper spacing
 * - Medium font weight for emphasis
 * - Small font size for compact layout
 * - Black color for readability
 * - Proper margin for visual separation
 */
.form-field label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  font-size: 12px;
  color: #000000;
}

/* ==================== INPUT FIELD STYLING ==================== */

/**
 * INPUT AND SELECT FIELDS
 * 
 * Base styling for all input types:
 * - Full width utilization
 * - Consistent padding and font size
 * - Light border with rounded corners
 * - Smooth transitions for state changes
 * - Proper box sizing for consistent sizing
 */
.form-field input,
.form-field select {
  width: 100%;
  max-width: 100%;
  padding: 8px 12px;
  font-size: 14px;
  border: 1px solid #ced4da;
  border-radius: 8px;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  box-sizing: border-box;
}

/* ==================== RESPONSIVE INPUT SIZING ==================== */

/**
 * TABLET RESPONSIVE INPUT STYLING (max-width: 768px)
 * 
 * Adjustments for tablet-sized screens:
 * - Larger font size to prevent iOS zoom
 * - Increased padding for touch interaction
 * - Maintained functionality and usability
 */
@media (max-width: 768px) {
  .form-field input,
  .form-field select {
    font-size: 16px; /* Prevent zoom on iOS */
    padding: 10px 12px;
  }
}

/**
 * MOBILE RESPONSIVE INPUT STYLING (max-width: 480px)
 * 
 * Adjustments for mobile-sized screens:
 * - Reduced padding for space efficiency
 * - Smaller font size for compact layout
 * - Maintained touch-friendly interactions
 */
@media (max-width: 480px) {
  .form-field input,
  .form-field select {
    padding: 8px 10px;
    font-size: 14px;
  }
}

/* ==================== FOCUS STATE STYLING ==================== */

/**
 * INPUT FOCUS STATE
 * 
 * Enhanced visual feedback when input is focused:
 * - Gray border color for subtle indication
 * - Box shadow for depth and emphasis
 * - Maintains accessibility standards
 * - Consistent across all input types
 */
.form-field input:focus,
.form-field select:focus {
  border-color: #9e9e9e;
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(158, 158, 158, 0.25);
}

/* ==================== FORM INPUT STYLING ==================== */

/**
 * FORM INPUT BASE STYLES
 * 
 * Consistent styling for form inputs:
 * - Flex-grow for responsive width
 * - 40px height for touch-friendly interaction
 * - Rounded corners with 8px border radius
 * - Poppins font for modern typography
 * - Smooth transitions for state changes
 * - Proper padding and box sizing
 */
.form-input {
  flex-grow: 1;
  height: 40px;
  border: 1px solid #DBDBDB;
  border-radius: 8px;
  font-family: 'Poppins', sans-serif;
  font-size: 16px;
  color: #222222;
  background: #FFFFFF;
  transition: all 0.3s ease;
  padding: 0 12px;
}

/* ==================== DISABLED/READONLY STATE STYLING ==================== */

/**
 * DISABLED/READONLY FORM INPUT
 * 
 * Comprehensive disabled state for form inputs:
 * - Gray background for visual indication
 * - Muted text color for reduced emphasis
 * - Disabled cursor for clear interaction state
 * - Gray border for consistent appearance
 * - Disabled pointer events for proper behavior
 */
.form-input[disabled],
.form-input[readonly] {
  background-color: #f1f3f4 !important;
  color: #5f6368 !important;
  cursor: not-allowed !important;
  border-color: #dadce0 !important;
  opacity: 1 !important;
}

/**
 * FORM INPUT FOCUS STATE
 * 
 * Enhanced visual feedback when form input is focused:
 * - Gray border color for subtle indication
 * - Box shadow for depth and emphasis
 * - Maintains accessibility standards
 */
.form-input:focus {
  outline: none;
  border-color: #9e9e9e;
  box-shadow: 0 0 0 2px rgba(158, 158, 158, 0.25);
}

/**
 * INVALID FORM INPUT STATE
 * 
 * Error state styling for validation feedback:
 * - Red border color for clear error indication
 * - Light red background for subtle emphasis
 * - Used for form validation errors
 */
.form-input.invalid-input {
  border-color: #f44336;
  background-color: rgba(244, 67, 54, 0.05);
}

/* ==================== CHECKBOX STYLING ==================== */

/**
 * CHECKBOX FIELD LAYOUT
 * 
 * Special layout for checkbox fields:
 * - Flexbox layout for horizontal alignment
 * - Row direction for label-input arrangement
 * - Proper gap spacing between elements
 * - Centered alignment for visual balance
 */
.form-field:has(input[type="checkbox"]) {
  display: flex !important;
  flex-direction: row !important;
  align-items: center !important;
  gap: 8px !important;
}

/**
 * CHECKBOX LABEL STYLING
 * 
 * Label styling for checkbox fields:
 * - No bottom margin for horizontal layout
 * - Flexbox alignment for proper positioning
 * - Order 2 for label after checkbox
 * - Proper gap spacing
 */
.form-field:has(input[type="checkbox"]) label {
  margin-bottom: 0 !important;
  display: flex !important;
  align-items: center !important;
  gap: 8px !important;
  order: 2;
}

/**
 * CHECKBOX INPUT STYLING
 * 
 * Checkbox input styling:
 * - Order 1 for checkbox before label
 * - Right margin for spacing
 * - Auto width for natural sizing
 * - No flex shrinking for consistent size
 */
.form-field:has(input[type="checkbox"]) input[type="checkbox"] {
  order: 1;
  margin-right: 8px;
}

/* ==================== FIELD INDICATORS ==================== */

/**
 * REQUIRED FIELD INDICATOR
 * 
 * Styling for mandatory field indicators:
 * - Red color for clear indication
 * - Bold font weight for emphasis
 * - Small left margin for spacing
 * - Used for required field validation
 */
.form-field label span {
  color: #dc3545;
  font-weight: bold;
  margin-left: 2px;
}

/**
 * NO INPUT INDICATOR
 * 
 * Styling for read-only field indicators:
 * - Red color for clear indication
 * - Small font size for subtle appearance
 * - Normal font weight for readability
 * - Italic style for emphasis
 */
.no-input-indicator {
  color: #e74c3c;
  font-size: 11px;
  font-weight: normal;
  font-style: italic;
}

/* ==================== GLOBAL DISABLED/READONLY STYLING ==================== */

/**
 * UNIFIED DISABLED/READONLY STYLING
 * 
 * Comprehensive disabled state for all input types:
 * - Gray background for visual indication
 * - Muted text color for reduced emphasis
 * - Disabled cursor for clear interaction state
 * - Gray border for consistent appearance
 * - Disabled pointer events for proper behavior
 */
input[disabled],
select[disabled],
textarea[disabled],
input[readonly],
select[readonly],
textarea[readonly] {
  background-color: #f1f3f4 !important;
  color: #5f6368 !important;
  cursor: not-allowed !important;
  border-color: #dadce0 !important;
  opacity: 1 !important;
}

/**
 * FORM FIELD SPECIFIC DISABLED/READONLY STYLING
 * 
 * Higher specificity rules for form field inputs:
 * - Ensures proper styling inheritance
 * - Overrides default form field styles
 * - Maintains consistency across all input types
 */
.form-field input[disabled],
.form-field select[disabled],
.form-field textarea[disabled],
.form-field input[readonly],
.form-field select[readonly],
.form-field textarea[readonly] {
  background-color: #f1f3f4 !important;
  color: #5f6368 !important;
  cursor: not-allowed !important;
  border-color: #dadce0 !important;
  opacity: 1 !important;
}

/* ==================== FOCUS STATE PROTECTION ==================== */

/**
 * DISABLED/READONLY FOCUS STATE PROTECTION
 * 
 * Additional protection for focus states on disabled fields:
 * - Prevents focus styling on disabled fields
 * - Maintains disabled appearance during focus
 * - Ensures consistent behavior across browsers
 * - Covers all input types for complete consistency
 */
input[type="text"][disabled]:focus,
input[type="number"][disabled]:focus,
input[type="date"][disabled]:focus,
input[type="checkbox"][disabled]:focus,
input[type="text"][readonly]:focus,
input[type="number"][readonly]:focus,
input[type="date"][readonly]:focus,
input[type="checkbox"][readonly]:focus {
  background-color: #f1f3f4 !important;
  color: #5f6368 !important;
  border-color: #dadce0 !important;
  box-shadow: none !important;
  outline: none !important;
}

/* ==================== USER INTERACTION PREVENTION ==================== */

/**
 * DISABLED FIELD INTERACTION PREVENTION
 * 
 * Ensures no user interaction is possible on disabled fields:
 * - Disabled pointer events for click prevention
 * - Disabled user selection for text prevention
 * - Cross-browser compatibility for user-select
 * - Prevents any form of interaction
 */
input[disabled],
select[disabled],
textarea[disabled] {
  pointer-events: none !important;
  user-select: none !important;
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
}

/* ==================== SPECIFIC INPUT TYPE COVERAGE ==================== */

/**
 * SPECIFIC INPUT TYPE DISABLED/READONLY STYLING
 * 
 * Complete coverage for all input types:
 * - Text inputs for string values
 * - Number inputs for numeric values
 * - Date inputs for date values
 * - Checkbox inputs for boolean values
 * - Ensures consistent behavior across all types
 */
input[type="text"][disabled],
input[type="number"][disabled],
input[type="date"][disabled],
input[type="checkbox"][disabled],
input[type="text"][readonly],
input[type="number"][readonly],
input[type="date"][readonly],
input[type="checkbox"][readonly] {
  background-color: #f1f3f4 !important;
  color: #5f6368 !important;
  cursor: not-allowed !important;
  border-color: #dadce0 !important;
  opacity: 1 !important;
}

/**
 * FORM FIELD SPECIFIC INPUT TYPE STYLING
 * 
 * Higher specificity for form field input types:
 * - Ensures proper styling inheritance
 * - Overrides default form field styles
 * - Maintains consistency across all contexts
 * - Complete coverage for all input types
 */
.form-field input[type="text"][disabled],
.form-field input[type="number"][disabled],
.form-field input[type="date"][disabled],
.form-field input[type="checkbox"][disabled],
.form-field input[type="text"][readonly],
.form-field input[type="number"][readonly],
.form-field input[type="date"][readonly],
.form-field input[type="checkbox"][readonly] {
  background-color: #f1f3f4 !important;
  color: #5f6368 !important;
  cursor: not-allowed !important;
  border-color: #dadce0 !important;
  opacity: 1 !important;
}

/* ==================== HOVER STATE PROTECTION ==================== */

/**
 * DISABLED/READONLY HOVER STATE PROTECTION
 * 
 * Prevents hover effects on disabled fields:
 * - Maintains disabled appearance during hover
 * - Consistent behavior across all input types
 * - Prevents visual confusion for users
 * - Covers both global and form field specific selectors
 */
input[disabled]:hover,
select[disabled]:hover,
textarea[disabled]:hover,
input[readonly]:hover,
select[readonly]:hover,
textarea[readonly]:hover,
.form-field input[disabled]:hover,
.form-field select[disabled]:hover,
.form-field textarea[disabled]:hover,
.form-field input[readonly]:hover,
.form-field select[readonly]:hover,
.form-field textarea[readonly]:hover {
  background-color: #f1f3f4 !important;
  border-color: #dadce0 !important;
}

/* ==================== FOCUS STATE PROTECTION ==================== */

/**
 * DISABLED/READONLY FOCUS STATE PROTECTION
 * 
 * Prevents focus effects on disabled fields:
 * - Maintains disabled appearance during focus
 * - Removes box shadow and outline
 * - Consistent behavior across all input types
 * - Covers both global and form field specific selectors
 */
input[disabled]:focus,
select[disabled]:focus,
textarea[disabled]:focus,
input[readonly]:focus,
select[readonly]:focus,
textarea[readonly]:focus,
.form-field input[disabled]:focus,
.form-field select[disabled]:focus,
.form-field textarea[disabled]:focus,
.form-field input[readonly]:focus,
.form-field select[readonly]:focus,
.form-field textarea[readonly]:focus {
  background-color: #f1f3f4 !important;
  border-color: #dadce0 !important;
  box-shadow: none !important;
  outline: none !important;
}

/* ==================== RESPONSIVE DESIGN ==================== */

/**
 * TABLET RESPONSIVE NO-INPUT INDICATOR (max-width: 768px)
 * 
 * Adjustments for tablet-sized screens:
 * - Smaller font size for space efficiency
 * - Maintained readability and emphasis
 * - Consistent with overall responsive design
 */
@media (max-width: 768px) {
  .no-input-indicator {
    font-size: 10px;
  }
}

/**
 * MOBILE RESPONSIVE NO-INPUT INDICATOR (max-width: 480px)
 * 
 * Adjustments for mobile-sized screens:
 * - Minimal font size for compact layout
 * - Maintained visibility and emphasis
 * - Optimized for small screen space
 */
@media (max-width: 480px) {
  .no-input-indicator {
    font-size: 9px;
  }
}

/* ==================== CHECKBOX RESPONSIVE STYLING ==================== */

/**
 * CHECKBOX INPUT BASE STYLING
 * 
 * Base styling for checkbox inputs:
 * - Right margin for spacing from label
 * - Vertical alignment for proper positioning
 * - Auto width for natural sizing
 * - No flex shrinking for consistent size
 */
input[type="checkbox"] {
  margin-right: 8px;
  vertical-align: middle;
  width: auto !important;
  flex-shrink: 0;
}

/**
 * TABLET RESPONSIVE CHECKBOX LAYOUT (max-width: 768px)
 * 
 * Adjustments for tablet-sized screens:
 * - Maintained horizontal layout
 * - Reduced gap for space efficiency
 * - Smaller right margin for compact spacing
 * - Maintained touch-friendly interactions
 */
@media (max-width: 768px) {
  .form-field:has(input[type="checkbox"]) {
    flex-direction: row !important;
    align-items: center !important;
    gap: 6px !important;
  }

  input[type="checkbox"] {
    margin-right: 6px;
  }
}

/**
 * MOBILE RESPONSIVE CHECKBOX LAYOUT (max-width: 480px)
 * 
 * Adjustments for mobile-sized screens:
 * - Minimal gap for compact layout
 * - Reduced right margin for space efficiency
 * - Maintained functionality on small screens
 * - Optimized for touch interaction
 */
@media (max-width: 480px) {
  .form-field:has(input[type="checkbox"]) {
    gap: 4px !important;
  }

  input[type="checkbox"] {
    margin-right: 4px;
  }
}
