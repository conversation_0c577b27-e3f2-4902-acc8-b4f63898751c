/**
 * FORM HEADER COMPONENT
 * 
 * This component provides the header section of dynamic forms with action buttons
 * and form state management. It serves as the primary navigation and control
 * interface for form operations.
 * 
 * PURPOSE:
 * - Provides action buttons for form operations (Submit, Validate, Authorize, etc.)
 * - Handles view mode toggling (edit vs view)
 * - Displays error messages and form status
 * - Manages form state and navigation
 * 
 * FEATURES:
 * - Action buttons for different form operations
 * - View mode toggle functionality
 * - Error message display
 * - Responsive design with Material Design components
 * - Authorization and validation support
 * 
 * USAGE:
 * <app-form-header 
 *   [form]="form"
 *   [isViewMode]="false"
 *   [isAuth]="true"
 *   [isRowView]="false"
 *   [errorMessage]="''"
 *   (toggleViewMode)="onToggleViewMode()"
 *   (submitForm)="onSubmitForm()"
 *   (validateRecord)="onValidateRecord()"
 *   (authorizeRecord)="onAuthorizeRecord()"
 *   (goBack)="onGoBack()"
 *   (rejectRecord)="onRejectRecord()"
 *   (deleteRecord)="onDeleteRecord()">
 * </app-form-header>
 */

import { Component, Input, Output, EventEmitter } from '@angular/core';
import { FormGroup, ReactiveFormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';

// Angular Material imports for UI components
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';

/**
 * Form Header Component
 * 
 * Provides the header interface for dynamic forms with the following capabilities:
 * - Form action buttons (Submit, Validate, Authorize, Reject, Delete)
 * - View mode toggle (edit vs read-only view)
 * - Error message display
 * - Navigation controls (Back button)
 * - Authorization workflow support
 * 
 * FORM OPERATIONS:
 * - Submit: Saves the form data
 * - Validate: Validates form data before submission
 * - Authorize: Approves the record (authorization workflow)
 * - Reject: Rejects the record (authorization workflow)
 * - Delete: Removes the record
 * 
 * VIEW MODES:
 * - Edit Mode: Full form interaction
 * - View Mode: Read-only display
 * - Row View: Compact display mode
 * 
 * AUTHORIZATION:
 * - Supports authorization workflow with approve/reject actions
 * - Handles different authorization levels
 * - Manages authorization state and permissions
 */
@Component({
  selector: 'app-form-header',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatButtonModule,
    MatIconModule,
    MatTooltipModule
  ],
  templateUrl: './form-header.component.html',
  styleUrl: './form-header.component.scss'
})
export class FormHeaderComponent {
  
  // ==================== CORE INPUTS ====================
  
  /** Angular FormGroup for reactive form integration */
  @Input() form!: FormGroup;
  
  /** Whether the form is in view mode (read-only) */
  @Input() isViewMode: boolean = false;
  
  /** Whether authorization features are enabled */
  @Input() isAuth: boolean = true;
  
  /** Whether the form is in row view mode (compact display) */
  @Input() isRowView: boolean = false;
  
  /** Error message to display in the header */
  @Input() errorMessage: string = '';
  
  // ==================== OUTPUT EVENTS ====================
  
  /** Emitted when user toggles between edit and view modes */
  @Output() toggleViewMode = new EventEmitter<void>();
  
  /** Emitted when user submits the form */
  @Output() submitForm = new EventEmitter<void>();
  
  /** Emitted when user validates the form data */
  @Output() validateRecord = new EventEmitter<void>();
  
  /** Emitted when user authorizes the record */
  @Output() authorizeRecord = new EventEmitter<void>();
  
  /** Emitted when user wants to go back */
  @Output() goBack = new EventEmitter<void>();
  
  /** Emitted when user rejects the record */
  @Output() rejectRecord = new EventEmitter<void>();
  
  /** Emitted when user deletes the record */
  @Output() deleteRecord = new EventEmitter<void>();

  // ==================== ACTION HANDLERS ====================
  
  /**
   * Handles view mode toggle button click
   * Emits event to switch between edit and view modes
   */
  onToggleViewMode(): void {
    this.toggleViewMode.emit();
  }

  /**
   * Handles form submission button click
   * Emits event to submit the form data
   */
  onSubmitForm(): void {
    this.submitForm.emit();
  }

  /**
   * Handles form validation button click
   * Emits event to validate the form data
   */
  onValidateRecord(): void {
    this.validateRecord.emit();
  }

  /**
   * Handles record authorization button click
   * Emits event to authorize the record
   */
  onAuthorizeRecord(): void {
    this.authorizeRecord.emit();
  }

  /**
   * Handles back navigation button click
   * Emits event to navigate back
   */
  onGoBack(): void {
    this.goBack.emit();
  }

  /**
   * Handles record rejection button click
   * Emits event to reject the record
   */
  onRejectRecord(): void {
    this.rejectRecord.emit();
  }

  /**
   * Handles record deletion button click
   * Emits event to delete the record
   */
  onDeleteRecord(): void {
    this.deleteRecord.emit();
  }
}
