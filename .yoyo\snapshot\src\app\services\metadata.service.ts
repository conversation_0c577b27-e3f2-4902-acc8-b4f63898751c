import { Injectable,inject } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../environments/environment';

@Injectable({
  providedIn: 'root'
})
export class MetadataService {
private menuUrl = `${environment.baseURL}/api/menu/`; // Base URL for menu API
  private tableMetadataUrl = `${environment.baseURL}/api/merged/`; // Base URL for table metadata
  private screenMetadataUrl = `${environment.baseURL}/api/merged/`; // Base URL for table metadata
private http = inject(HttpClient);
  constructor() { }

  getMenu(application: string): Observable<any> {
    return this.http.get(`${this.menuUrl}${application}`, { withCredentials: true });
  }
  getTableMetadata(tableName: string): Observable<any> {
    return this.http.get(`${this.tableMetadataUrl}${tableName}/metadata`, { withCredentials: true });
  }
  getScreenMetadata(tableName: string): Observable<any> {
    return this.http.get(`${this.screenMetadataUrl}${tableName}/metadata`, { withCredentials: true });
  }
  getScreen(screenId: string): Observable<any> {
    // TODO: Implement screen fetching logic
    return new Observable(); 
  }
  getCriteriaFields(queryName: string): Observable<any[]> {
    return this.http.get<any[]>(`${environment.baseURL}/api/query-builder/${queryName}`, { withCredentials: true });
  }
}
