/**
 * FORM ACTIONS COMPONENT STYLES
 * 
 * This SCSS file provides comprehensive styling for the form actions component
 * with support for success popups, button themes, and responsive design.
 * 
 * DESIGN SYSTEM:
 * - Uses shared styles for consistency
 * - Material Design color palette for buttons
 * - Gradient backgrounds for modern appearance
 * - Consistent spacing and border radius
 * - Smooth transitions for better UX
 * 
 * LAYOUT STRUCTURE:
 * - Success popup modal with overlay
 * - Button group layouts for different contexts
 * - Responsive design with mobile breakpoints
 * - Message styling for success and error states
 * 
 * BUTTON THEMES:
 * - Toggle View: Teal (#009688) for view switching
 * - Submit: Green (#4CAF50) for form submission
 * - Validate: Blue (#2196F3) for validation
 * - Authorize: <PERSON> (#9C27B0) for authorization
 * - Back: Gray (#607D8B) for navigation
 * - Reject: Red (#F44336) for rejection
 * - Delete: Dark Red (#D32F2F) for deletion
 * 
 * RESPONSIVE DESIGN:
 * - Desktop-first approach with mobile breakpoints
 * - Breakpoints at 768px and 480px
 * - Flexible layouts that adapt to screen size
 * - Touch-friendly button sizes on mobile
 */

@use '../../../../styles/shared.scss' as *;

/* ==================== SUCCESS POPUP STYLING ==================== */

/**
 * SUCCESS POPUP MODAL
 * 
 * Modal popup for success messages:
 * - Fixed positioning for overlay effect
 * - Centered on screen with transform
 * - Responsive width with max-width constraint
 * - Clean white background with shadow
 * - High z-index for proper layering
 * - Rounded corners for modern appearance
 */
.popup {
  position: fixed; 
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 80%; 
  max-width: 400px; 
  background-color: white;
  padding: 20px;
  border-radius: 5px;
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.5); 
  z-index: 100; 
}

/**
 * POPUP CONTENT
 * 
 * Content container for popup:
 * - Centered text alignment
 * - Proper spacing and typography
 * - Clean layout for message display
 */
.popup-content {
  text-align: center;
}

/**
 * CLOSE BUTTON
 * 
 * Close button for popup dismissal:
 * - Positioned absolutely in top-right corner
 * - Large font size for easy clicking
 * - Pointer cursor for clear interaction
 * - Proper spacing from content
 */
.close {
  position: absolute;
  top: 10px;
  right: 15px;
  font-size: 20px;
  cursor: pointer;
}

/* ==================== BUTTON GROUP LAYOUTS ==================== */

/**
 * DEFAULT BUTTON GROUP
 * 
 * Standard button group layout:
 * - Inline flex with row-reverse direction
 * - Proper gap spacing between buttons
 * - No wrapping for consistent layout
 * - Aligned items for proper positioning
 * - White-space nowrap for text preservation
 */
.button-group {
  display: inline-flex !important;
  flex-direction: row-reverse !important;
  gap: 8px;
  margin-bottom: 16px;
  flex-wrap: nowrap !important;
  align-items: center;
  white-space: nowrap;
}

/**
 * CONTEXT-SPECIFIC BUTTON GROUPS
 * 
 * Button group layouts for different contexts:
 * - Row view layouts with standard direction
 * - Nested group sections with proper spacing
 * - Grouped field sections with consistent behavior
 * - No margins or padding for clean integration
 */
.row-view .button-group,
.row-view-table .button-group,
.row-view-nested .button-group,
.nested-group-section .button-group,
.grouped-field-section .button-group {
  display: inline-flex !important;
  flex-direction: row !important;
  flex-wrap: nowrap !important;
  margin: 0;
  padding: 0;
}

/* ==================== RESPONSIVE BUTTON GROUPS ==================== */

/**
 * TABLET RESPONSIVE BUTTON GROUPS (max-width: 768px)
 * 
 * Adjustments for tablet-sized screens:
 * - Reduced gap spacing for space efficiency
 * - Maintained layout structure
 * - Touch-friendly interactions
 */
@media (max-width: 768px) {
  .button-group {
    gap: 6px;
  }
}

/**
 * MOBILE RESPONSIVE BUTTON GROUPS (max-width: 480px)
 * 
 * Adjustments for mobile-sized screens:
 * - Minimal gap spacing for compact layout
 * - Optimized for small screen space
 * - Maintained functionality on mobile
 */
@media (max-width: 480px) {
  .button-group {
    gap: 4px;
  }
}

/* ==================== MESSAGE STYLING ==================== */

/**
 * SUCCESS MESSAGE
 * 
 * Styling for success message display:
 * - Green color for positive indication
 * - Bold font weight for emphasis
 * - Light green background for subtle emphasis
 * - Border for visual definition
 * - Rounded corners for modern appearance
 * - Proper spacing and margins
 */
.success-message {
  color: #28a745;
  font-size: 14px;
  font-weight: bold;
  padding: 10px;
  border: 1px solid #c3e6cb;
  background-color: #d4edda;
  border-radius: 4px;
  margin-bottom: 10px;
}

/**
 * ERROR MESSAGE
 * 
 * Styling for error message display:
 * - Light red background for error indication
 * - Red border for visual definition
 * - Dark red text for readability
 * - Proper padding and margins
 * - Rounded corners for modern appearance
 */
.error-message {
  background-color: #fdd; /* Light red background */
  border: 1px solid #faa; /* Red border */
  color: #a00; /* Dark red text color */
  padding: 10px;
  margin-bottom: 10px; 
  border-radius: 4px; /* Slightly rounded corners */
}

/* ==================== MATERIAL SNACKBAR STYLING ==================== */

/**
 * MATERIAL SNACKBAR CONTAINER
 * 
 * Custom styling for Material Design snackbars:
 * - Success snackbar with green theme
 * - Error snackbar with red theme
 * - Consistent button and text colors
 * - Proper contrast for accessibility
 */
.mat-mdc-snack-bar-container {
  &.success-snackbar {
    --mdc-snackbar-container-color: #4caf50;
    --mat-mdc-snack-bar-button-color: #fff;
    --mdc-snackbar-supporting-text-color: #fff;
  }

  &.error-snackbar {
    --mdc-snackbar-container-color: #f44336;
    --mat-mdc-snack-bar-button-color: #fff;
    --mdc-snackbar-supporting-text-color: #fff;
  }
}

/* ==================== BUTTON THEMES ==================== */

/**
 * TOGGLE VIEW BUTTON - TEAL THEME
 * 
 * Styling for the view toggle button:
 * - Teal gradient background for modern appearance
 * - White text for contrast
 * - Hover effects with darker gradient
 * - Transform and shadow effects for interactivity
 * - Used for switching between view modes
 */
.form-action-button.toggle-view-button {
  border-color: #009688;
  background: linear-gradient(135deg, #009688 0%, #00796B 100%);
  color: white;
  
  &:hover {
    background: linear-gradient(135deg, #00796B 0%, #004D40 100%);
    transform: translateY(-1px);
  }
  
  &:active {
    transform: translateY(0);
    box-shadow: 0 2px 6px rgba(0, 150, 136, 0.3);
  }
}

/**
 * SUBMIT BUTTON - GREEN THEME
 * 
 * Styling for the submit button:
 * - Green gradient background for positive action
 * - White text for contrast
 * - Hover effects with darker gradient
 * - Disabled state with gray styling
 * - Used for form submission
 */
.form-action-button.submit-button {
  border-color: #4CAF50;
  background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
  color: white;
  
  &:hover {
    background: linear-gradient(135deg, #45a049 0%, #3d8b40 100%);
    transform: translateY(-1px);
  }
  
  &:active {
    transform: translateY(0);
    box-shadow: 0 2px 6px rgba(76, 175, 80, 0.3);
  }
  
  &:disabled {
    background: #cccccc !important;
    color: #666666 !important;
    border-color: #cccccc !important;
    cursor: not-allowed !important;
    transform: none !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
  }
}

/**
 * VALIDATE BUTTON - BLUE THEME
 * 
 * Styling for the validate button:
 * - Blue gradient background for validation action
 * - White text for contrast
 * - Hover effects with darker gradient
 * - Transform and shadow effects for interactivity
 * - Used for form validation
 */
.form-action-button.validate-button {
  border-color: #2196F3;
  background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
  color: white;
  
  &:hover {
    background: linear-gradient(135deg, #1976D2 0%, #1565C0 100%);
    transform: translateY(-1px);
  }
  
  &:active {
    transform: translateY(0);
    box-shadow: 0 2px 6px rgba(33, 150, 243, 0.3);
  }
}

/**
 * AUTHORIZE BUTTON - PURPLE THEME
 * 
 * Styling for the authorize button:
 * - Purple gradient background for authorization
 * - White text for contrast
 * - Hover effects with darker gradient
 * - Transform and shadow effects for interactivity
 * - Used for record authorization
 */
.form-action-button.authorize-button {
  border-color: #9C27B0;
  background: linear-gradient(135deg, #9C27B0 0%, #7B1FA2 100%);
  color: white;
  
  &:hover {
    background: linear-gradient(135deg, #7B1FA2 0%, #6A1B9A 100%);
    transform: translateY(-1px);
  }
  
  &:active {
    transform: translateY(0);
    box-shadow: 0 2px 6px rgba(156, 39, 176, 0.3);
  }
}

/**
 * BACK BUTTON - GRAY THEME
 * 
 * Styling for the back button:
 * - Gray gradient background for navigation
 * - White text for contrast
 * - Hover effects with darker gradient
 * - Transform and shadow effects for interactivity
 * - Used for navigation back
 */
.form-action-button.back-button {
  border-color: #607D8B;
  background: linear-gradient(135deg, #607D8B 0%, #455A64 100%);
  color: white;
  
  &:hover {
    background: linear-gradient(135deg, #455A64 0%, #37474F 100%);
    transform: translateY(-1px);
  }
  
  &:active {
    transform: translateY(0);
    box-shadow: 0 2px 6px rgba(96, 125, 139, 0.3);
  }
}

/**
 * REJECT BUTTON - RED THEME
 * 
 * Styling for the reject button:
 * - Red gradient background for rejection action
 * - White text for contrast
 * - Hover effects with darker gradient
 * - Transform and shadow effects for interactivity
 * - Used for record rejection
 */
.form-action-button.reject-button {
  border-color: #F44336;
  background: linear-gradient(135deg, #F44336 0%, #D32F2F 100%);
  color: white;
  
  &:hover {
    background: linear-gradient(135deg, #D32F2F 0%, #C62828 100%);
    transform: translateY(-1px);
  }
  
  &:active {
    transform: translateY(0);
    box-shadow: 0 2px 6px rgba(244, 67, 54, 0.3);
  }
}

/**
 * DELETE BUTTON - DARK RED THEME
 * 
 * Styling for the delete button:
 * - Dark red gradient background for destructive action
 * - White text for contrast
 * - Hover effects with darker gradient
 * - Transform and shadow effects for interactivity
 * - Used for record deletion
 */
.form-action-button.delete-button {
  border-color: #D32F2F;
  background: linear-gradient(135deg, #D32F2F 0%, #B71C1C 100%);
  color: white;
  
  &:hover {
    background: linear-gradient(135deg, #B71C1C 0%, #8E0000 100%);
    transform: translateY(-1px);
  }
  
  &:active {
    transform: translateY(0);
    box-shadow: 0 2px 6px rgba(211, 47, 47, 0.3);
  }
}

/* ==================== RESPONSIVE BUTTON STYLING ==================== */

/**
 * TABLET RESPONSIVE BUTTON STYLING (max-width: 768px)
 * 
 * Adjustments for tablet-sized screens:
 * - Reduced padding for space efficiency
 * - Smaller font size for better fit
 * - Larger icons for touch interaction
 * - Maintained functionality and usability
 */
@media (max-width: 768px) {
  .form-action-button {
    padding: 6px 12px;
    font-size: 13px;
    
    mat-icon {
      font-size: 16px;
      width: 16px;
      height: 16px;
    }
  }
}

/**
 * MOBILE RESPONSIVE BUTTON STYLING (max-width: 480px)
 * 
 * Adjustments for mobile-sized screens:
 * - Minimal padding for compact layout
 * - Smaller font size for space efficiency
 * - Reduced gap for tighter spacing
 * - Smaller icons for mobile optimization
 * - Maintained touch-friendly interactions
 */
@media (max-width: 480px) {
  .form-action-button {
    padding: 5px 10px;
    font-size: 12px;
    gap: 6px;
    
    mat-icon {
      font-size: 14px;
      width: 14px;
      height: 14px;
    }
  }
}
