<!-- 
  FORM ACTIONS COMPONENT TEMPLATE
  
  This template provides the success popup functionality for form actions.
  It displays a modal popup when form operations are completed successfully.
  
  STRUCTURE:
  - Success popup modal with overlay
  - Centered popup content with close button
  - Success message display
  - Responsive design for different screen sizes
  
  FEATURES:
  - Modal popup with backdrop
  - Close button for user dismissal
  - Success message display
  - Responsive sizing and positioning
  - Z-index management for proper layering
  
  USAGE:
  - Automatically shown when showSuccessPopup is true
  - Displays success message for form operations
  - User can close via close button
  - Auto-dismisses after timeout (handled in component)
  
  ACCESSIBILITY:
  - Proper modal structure
  - Close button for keyboard navigation
  - Screen reader compatible
  - Focus management
-->

<!-- 
  SUCCESS POPUP MODAL
  - Fixed positioning for overlay effect
  - Centered on screen with transform
  - High z-index for proper layering
  - Responsive width with max-width constraint
  - Clean white background with shadow
  - Rounded corners for modern appearance
-->
@if (showSuccessPopup) {
  <div class="popup">
    
    <!-- 
      POPUP CONTENT
      - Centered text alignment
      - Close button positioned absolutely
      - Success message paragraph
      - Proper spacing and typography
    -->
    <div class="popup-content">
      
      <!-- 
        CLOSE BUTTON
        - Positioned in top-right corner
        - Large click target for usability
        - Times symbol (×) for clear meaning
        - Click handler for dismissal
        - Proper cursor styling
      -->
      <span class="close" (click)="closeSuccessPopup()">&times;</span>
      
      <!-- 
        SUCCESS MESSAGE
        - Clear success indication
        - Consistent messaging
        - Proper typography and spacing
        - User-friendly confirmation text
      -->
      <p>Record Inserted Successfully</p>
    </div>
  </div>
}
