import { AuthenticationService } from './../services/authentication.service';
import { NavigationService } from '../services/navigation.service';
import { Component, inject, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { CommonModule } from '@angular/common';
import { AuthError } from '../core/models/auth.models';

@Component({
  selector: 'app-login',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatCheckboxModule,
    MatIconModule,
    MatProgressSpinnerModule
  ],
  templateUrl: './login.component.html',
  styleUrl: './login.component.scss'
})
export class LoginComponent implements OnInit {
  loginForm!: FormGroup;
  errorMessage: string = '';
  isLoading: boolean = false;
  hidePassword: boolean = true;
  animationsLoaded: boolean = false;

  private authenticationService = inject(AuthenticationService);
  private navigationService = inject(NavigationService);
  private router = inject(Router);
  private fb = inject(FormBuilder);

  ngOnInit(): void {
    // Initialize form immediately for faster rendering
    this.initializeForm();

    // Defer non-critical operations to improve initial load time
    setTimeout(() => {
      this.loadRememberedUsername();
      // Enable animations after initial render is complete
      this.animationsLoaded = true;
    }, 100);
  }

  private initializeForm(): void {
    this.loginForm = this.fb.group({
      username: ['', [Validators.required]],
      password: ['', [Validators.required]], // Remove minLength - let backend handle validation
      rememberMe: [false] // Frontend-only field for UI state
    });
  }

  private loadRememberedUsername(): void {
    const rememberedUsername = localStorage.getItem('rememberedUsername');
    if (rememberedUsername) {
      this.loginForm.patchValue({
        username: rememberedUsername,
        rememberMe: true
      });
    }
  }

  togglePasswordVisibility(): void {
    this.hidePassword = !this.hidePassword;
  }

  onSubmit(): void {
    if (this.loginForm.valid) {
      this.errorMessage = '';
      this.isLoading = true;

      // Extract only the credentials that backend expects (username, password)
      const credentials = {
        username: this.loginForm.value.username,
        password: this.loginForm.value.password
      };

      // Handle rememberMe locally if needed (frontend-only feature)
      const rememberMe = this.loginForm.value.rememberMe;
      if (rememberMe) {
        // Store username for future logins if remember me is checked
        localStorage.setItem('rememberedUsername', credentials.username);
      } else {
        localStorage.removeItem('rememberedUsername');
      }

      // Using the existing authentication service with backend-expected format
      this.authenticationService.loginWithProfile(credentials).subscribe({
        next: ({ response, profile }) => {
          this.isLoading = false;
          if (response.success && profile) {
            // Sugar method to save profile 
        //     if (response['privileges']) {
        //  localStorage.setItem('userPrivileges',
        //   JSON.stringify(response['privileges']));
        //     }
            this.authenticationService.saveUserProfile(profile);
            this.navigationService.navigateToHome();
          } else if (response.success && !profile) {
            // Login successful but profile fetch failed
            console.warn('Login successful but failed to fetch profile');
            this.navigationService.navigateToHome();
          } else {
            this.errorMessage = response.message || 'Invalid username or password.';
          }
        },
        error: (authError: AuthError) => {
          this.isLoading = false;
          // Enhanced error handling with typed errors
          switch (authError.type) {
            case 'NO_PASSWORD_SET':
              localStorage.setItem('canAccessSetPassword', 'true');
              sessionStorage.setItem('tempUsername', credentials.username);
              this.navigationService.navigateToSetPassword();
              break;

            case 'INVALID_CREDENTIALS':
            case 'NETWORK_ERROR':
            case 'UNKNOWN':
            default:
              this.errorMessage = authError.message;
              break;
          }
        }
      });
    } else {
      this.markFormGroupTouched();
    }
  }

  private markFormGroupTouched(): void {
    Object.keys(this.loginForm.controls).forEach(key => {
      const control = this.loginForm.get(key);
      control?.markAsTouched();
    });
  }

  getErrorMessage(fieldName: string): string {
    const control = this.loginForm.get(fieldName);
    if (control?.hasError('required')) {
      return `${fieldName.charAt(0).toUpperCase() + fieldName.slice(1)} is required`;
    }
    // Removed minLength validation - backend handles all password validation
    return '';
  }
}
