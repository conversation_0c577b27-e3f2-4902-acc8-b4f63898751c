<!-- 
  R<PERSON><PERSON><PERSON>R FIELD COMPONENT TEMPLATE
  
  This template handles regular form fields in dynamic forms, including text inputs,
  dropdowns, and other field types. It integrates with the unified dropdown component
  for dropdown fields and manages form control state.
  
  STRUCTURE:
  - Form field container with reactive form integration
  - Dynamic label with mandatory and readonly indicators
  - Conditional rendering for different field types
  - Dropdown integration for foreign key fields
  - Input fields for various data types
  
  FIELD TYPES SUPPORTED:
  - Foreign Key: Uses unified dropdown component
  - Boolean: Checkbox input
  - String: Text input with placeholder
  - Integer: Number input
  - Date: Date input
  - Double: Number input with decimal step
  
  FEATURES:
  - Dynamic field rendering based on field type
  - Dropdown integration for foreign key fields
  - Form control state management (enabled/disabled)
  - Handles field value changes and validation
  - Supports multi-field arrays and nested forms
  - Mandatory field indicators
  - Read-only field indicators
  
  ACCESSIBILITY:
  - Proper label associations
  - Form control integration
  - Keyboard navigation support
  - Screen reader compatibility
  - Disabled state management
-->

<!-- 
  REGULAR FIELD CONTAINER
  - FormGroup integration for reactive forms
  - Full width utilization
  - Proper spacing and layout
  - Responsive design support
-->
<div class="form-field" [formGroup]="form">
  
  <!-- 
    <PERSON><PERSON><PERSON><PERSON>C FIELD LABEL
    - Uses field label or field name as fallback
    - Mandatory indicator (*) for required fields
    - Read-only indicator for no-input fields
    - Proper accessibility with for attribute
  -->
  <label [for]="field.fieldName">
    {{ field.label || field.fieldName }} 
    @if (field.mandatory) {
      <span>*</span>
    }
    @if (field.noInput) {
      <span class="no-input-indicator"> (Read Only)</span>
    }
  </label>

  <!-- 
    FOREIGN KEY FIELD RENDERING
    - Uses unified dropdown component
    - Configurable dropdown settings
    - Form control integration
    - Disabled/readonly state support
    - Value change event handling
  -->
  @if (field.foreginKey) {
    <app-dropdown
      [fieldName]="field.fieldName"
      [formControl]="getFormControl(field.fieldName)"
      [config]="getRegularDropdownConfig(field)"
      [isDisabled]="isViewMode || field.noInput"
      [isReadonly]="isViewMode || field.noInput"
      [fields]="fields"
      [inputId]="field.fieldName"
      (valueChange)="onDropdownValueChange($event)">
    </app-dropdown>
  } 
  
  <!-- 
    REGULAR INPUT FIELDS
    - Conditional rendering based on field type
    - Form control integration for each type
    - Proper input attributes and validation
    - Disabled/readonly state management
    - Placeholder text for better UX
  -->
  @else {
    
    <!-- 
      BOOLEAN FIELD
      - Checkbox input for true/false values
      - Form control integration
      - Disabled state for no-input fields
      - Readonly state for view mode
    -->
    @if (field.type === 'boolean') {
      <input [formControlName]="field.fieldName" 
             [id]="field.fieldName"
             type="checkbox" 
             [readonly]="isViewMode || field.noInput" 
             [disabled]="field.noInput" />
    }
    
    <!-- 
      STRING FIELD
      - Text input for string values
      - Placeholder from field label or name
      - Form control integration
      - Disabled/readonly state management
    -->
    @if (field.type === 'string') {
      <input [formControlName]="field.fieldName" 
             [id]="field.fieldName"
             type="text" 
             [readonly]="isViewMode || field.noInput" 
             [disabled]="field.noInput" 
             [placeholder]="(field.label?.trim() || field.fieldName)" />
    }
    
    <!-- 
      INTEGER FIELD
      - Number input for integer values
      - Form control integration
      - Disabled/readonly state management
      - No decimal places for integers
    -->
    @if (field.type === 'int') {
      <input [formControlName]="field.fieldName" 
             [id]="field.fieldName"
             type="number" 
             [readonly]="isViewMode || field.noInput" 
             [disabled]="field.noInput" />
    }
    
    <!-- 
      DATE FIELD
      - Date input for date values
      - Form control integration
      - Disabled/readonly state management
      - Native date picker support
    -->
    @if (field.type === 'date') {
      <input [formControlName]="field.fieldName" 
             [id]="field.fieldName"
             type="date" 
             [readonly]="isViewMode || field.noInput" 
             [disabled]="field.noInput" />
    }
    
    <!-- 
      DOUBLE FIELD
      - Number input for decimal values
      - Step attribute for decimal precision
      - Form control integration
      - Disabled/readonly state management
    -->
    @if (field.type === 'double') {
      <input [formControlName]="field.fieldName" 
             [id]="field.fieldName"
             type="number" 
             step="00.50" 
             [readonly]="isViewMode || field.noInput" 
             [disabled]="field.noInput" />
    }
  }
</div>
