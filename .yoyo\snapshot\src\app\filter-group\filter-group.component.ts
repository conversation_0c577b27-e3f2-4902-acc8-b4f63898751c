import { Component, EventEmitter, Input, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';

@Component({
  selector: 'app-filter-group',
  standalone: true,
  templateUrl: './filter-group.component.html',
  styleUrls: ['./filter-group.component.scss'],
  imports: [
    CommonModule,
    FormsModule,
    MatFormFieldModule,
    MatSelectModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule,
  ],
})
export class FilterGroupComponent {
  @Input() group: any;
  @Input() fields: any[] = []; // These are the criteriaFields from DynamicQueryComponent
  @Input() canRemove: boolean = false;

  @Output() remove = new EventEmitter<void>();
  @Output() change = new EventEmitter<void>();
  @Output() fieldSelectedInCondition = new EventEmitter<any>(); // Emits the condition object

  addCondition() {
    const firstField = this.fields[0];
    this.group.conditions.push({
      field: firstField?.selectionField || '',
      operator: 'EQ',
      value: ''
    });
    this.emitChange();
  }

  removeCondition(index: number) {
    this.group.conditions.splice(index, 1);
    this.emitChange();
  }

  addSubGroup() {
    if (!this.group.children) {
      this.group.children = [];
    }
    this.group.children.push({
      logic: 'AND',
      conditions: [],
      children: []
    });
    this.emitChange();
  }

  removeChild(child: any) {
    if (this.group.children) {
      this.group.children = this.group.children.filter((g: any) => g !== child);
      this.emitChange();
    }
  }

  onRemoveGroup() {
    this.remove.emit();
  }

  private _determineFieldType(selectionFieldValue: string): string {
    const parts = selectionFieldValue.split(':');
    let determinedType = 'string'; // Default to string

    if (parts.length > 1) {
        const typePart = parts[parts.length - 1];
        if (typePart.endsWith('[]')) {
            determinedType = 'string'; // Treat array types like "CustomType[]" as string for operator compatibility
        } else {
            const simpleType = typePart.toLowerCase();
            if (['int', 'double', 'date', 'boolean', 'string'].includes(simpleType)) {
                determinedType = simpleType;
            } else {
                determinedType = 'string'; // Default for unknown simple types
            }
        }
    } else if (selectionFieldValue.endsWith('[]')) {
        // Field like "maritalStatus[]"
        determinedType = 'string';
    }
    // else: simple field name like "customer", defaults to 'string'

    // Ensure it's one of the known types for operators
    if (!['int', 'double', 'date', 'boolean', 'string'].includes(determinedType)) {
        return 'string';
    }
    return determinedType;
  }

  // getOperators method is removed as operators are now part of the condition object (condition.availableOperators)

  getInputType(fieldName: string): string {
    const field = this.fields.find(f => f.selectionField === fieldName);
    switch (field?.type) {
      case 'number': return 'number';
      case 'date': return 'date';
      default: return 'text';
    }
  }

  onFieldChange(condition: any) {
    // When the field changes, we need to notify the parent to fetch new operators.
    // The parent (DynamicQueryComponent) will then update condition.availableOperators and condition.operator.
    this.fieldSelectedInCondition.emit(condition);
    // We still call emitChange for other potential two-way bindings or parent logic.
    this.emitChange();
  }

  onConditionChange() {
    this.emitChange();
  }

  emitChange() {
    this.change.emit();
  }
}