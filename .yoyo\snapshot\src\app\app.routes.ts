import { Routes } from '@angular/router';
import { LoginComponent } from './login/login.component';
import { setPasswordGuard } from './auth/guards/set-password.guard';

export const routes: Routes = [
  {
    path: 'login',
    component: LoginComponent,
    title: 'Login'
  },
  {
    path: 'home',
    loadComponent: () => import('./home/<USER>').then(m => m.HomeComponent),
    title: 'Home'
  },
  {
    path: 'set-password',
    loadComponent: () => import('./set-password/set-password.component').then(m => m.SetPasswordComponent),
    canActivate: [setPasswordGuard],
    title: 'Set Password'
  },
  
  {
    path: '',
    redirectTo: '/login',
    pathMatch: 'full'
  },
  {
    path: '**',
    redirectTo: '/login'
  }
];
