import { Injectable } from '@angular/core';
import { Router } from '@angular/router';

@Injectable({
  providedIn: 'root'
})
export class NavigationService {

  constructor(private router: Router) { }

  /**
   * Navigate to login page
   */
  navigateToLogin(): void {
    this.router.navigate(['/login']);
  }

  /**
   * Navigate to home page
   */
  navigateToHome(): void {
    this.router.navigate(['/home']);
  }

  /**
   * Navigate to set password page
   */
  navigateToSetPassword(): void {
    this.router.navigate(['/set-password']);
  }

  /**
   * Navigate to any route with optional query parameters
   * @param route - The route to navigate to
   * @param queryParams - Optional query parameters
   */
  navigateTo(route: string, queryParams?: any): void {
    this.router.navigate([route], { queryParams });
  }

  /**
   * Navigate back in browser history
   */
  goBack(): void {
    window.history.back();
  }
}
