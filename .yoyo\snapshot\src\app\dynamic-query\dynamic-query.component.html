<mat-card class="query-builder-container">
  <mat-card-content>
    <div class="filter-groups-section">
      @for (group of filterGroups; track i; let i = $index) {
        <div class="filter-group-wrapper">
          <mat-card class="filter-group-card">
            <mat-card-header>
                <button mat-icon-button (click)="removeGroup(i)" title="Remove Root Group" class="remove-group-btn">
                  <mat-icon>delete_forever</mat-icon>
                </button>
            </mat-card-header>
            <mat-card-content>
              <!-- Logic to Previous Group -->
              @if (i > 0) {
                <mat-form-field class="logic-to-previous" appearance="fill">
                  <mat-label>Logic to Previous Group</mat-label>
                  <mat-select [(ngModel)]="group.logicToPrevious" (ngModelChange)="onGroupChange()">
                    <mat-option value="AND">AND</mat-option>
                    <mat-option value="OR">OR</mat-option>
                  </mat-select>
                </mat-form-field>
              }

              <!-- Filter Group Component -->
              <app-filter-group
                [group]="group"
                [fields]="criteriaFields"
                (change)="onGroupChange()"
                (fieldSelectedInCondition)="handleConditionFieldChange($event)">
              </app-filter-group>
            </mat-card-content>
          </mat-card>
        </div>
      }

      <!-- Action Buttons -->
      <div class="action-buttons responsive-button-group">
        <button mat-stroked-button color="accent" (click)="addGroup()">
          <mat-icon>add_circle_outline</mat-icon>
          <span class="button-text">Add Root Group</span>
        </button>
        <button mat-raised-button color="primary" (click)="submitQuery()">
          <mat-icon>send</mat-icon>
          <span class="button-text">Submit Query</span>
        </button>
      </div>

      <!-- Filter Expression Preview -->
      @if (filterExpression) {
        <div class="filter-preview">
          <mat-card>
            <mat-card-header>
              <mat-card-title>Filter Expression</mat-card-title>
            </mat-card-header>
            <mat-card-content>
              <pre>{{ filterExpression }}</pre>
            </mat-card-content>
          </mat-card>
        </div>
      }
    </div>
  </mat-card-content>
</mat-card>

<!-- Results Table -->
@if (showTable) {
  <div class="results-container">
    <mat-card>
      <mat-card-header class="results-header">
        <mat-card-title>Query Results ({{ results.length }} records)</mat-card-title>
        <button mat-raised-button color="primary" (click)="downloadPDF()" class="download-button">
          <mat-icon>download</mat-icon>
          <span class="button-text">Download PDF</span>
        </button>
      </mat-card-header>
      <mat-card-content>
        <div class="table-wrapper">
          <table mat-table [dataSource]="results" class="mat-elevation-z8 results-table responsive-mat-table">
            @for (column of columns; track column) {
              <ng-container [matColumnDef]="column">
                <th mat-header-cell *matHeaderCellDef>
                  <span class="header-text">{{ column | titlecase }}</span>
                </th>
                <td mat-cell *matCellDef="let element" [title]="element[column]">
                  <span class="cell-content">{{ element[column] }}</span>
                </td>
              </ng-container>
            }
            <tr mat-header-row *matHeaderRowDef="columns; sticky: true"></tr>
            <tr mat-row *matRowDef="let row; columns: columns;" 
                [class.clickable-row]="true"
                (click)="onRowClick(row)"></tr>
          </table>
        </div>
        
        <!-- No data message -->
        @if (results.length === 0) {
          <div class="no-data-message">
            <mat-icon>search_off</mat-icon>
            <h3>No Results Found</h3>
            <p>Try adjusting your search criteria and run the query again.</p>
          </div>
        }
      </mat-card-content>
    </mat-card>
  </div>
}