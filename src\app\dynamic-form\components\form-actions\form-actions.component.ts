/**
 * FORM ACTIONS COMPONENT
 * 
 * This component handles all form actions including submission, validation, authorization,
 * rejection, and deletion. It provides the business logic for form operations and
 * manages API communication for dynamic forms.
 * 
 * PURPOSE:
 * - Handles form submission with proper data transformation
 * - Manages form validation with server-side validation
 * - Supports authorization workflow (approve/reject)
 * - <PERSON>les record deletion with confirmation
 * - Manages loading states and error handling
 * - Provides success/error feedback to users
 * 
 * FEATURES:
 * - Form data transformation for API submission
 * - Server-side validation integration
 * - Authorization workflow support
 * - Error handling and user feedback
 * - Loading state management
 * - Success popup management
 * 
 * API INTEGRATION:
 * - POST /api/tables/{tableId}/records (Submit)
 * - POST /api/tables/{tableId}/validate (Validate)
 * - PUT /api/tables/{tableId}/records/{id}/authorize (Authorize)
 * - PUT /api/tables/{tableId}/records/{id}/reject (Reject)
 * - DELETE /api/tables/{tableId}/records/{id} (Delete)
 * 
 * USAGE:
 * <app-form-actions 
 *   [form]="form"
 *   [tableName]="'tableName'"
 *   [screenName]="'screenName'"
 *   [isTenantBasedFlag]="false"
 *   [authorizeNumber]="1"
 *   [isViewMode]="false"
 *   [showSuccessPopup]="false"
 *   [successMessage]="''"
 *   [errorMessage]="''"
 *   [isLoading]="false"
 *   [validationResult]="null"
 *   [fields]="fields"
 *   (submissionSuccess)="onSubmissionSuccess($event)"
 *   (errorMessageChange)="onErrorMessageChange($event)"
 *   (isLoadingChange)="onIsLoadingChange($event)"
 *   (showSuccessPopupChange)="onShowSuccessPopupChange($event)"
 *   (successMessageChange)="onSuccessMessageChange($event)"
 *   (validationResultChange)="onValidationResultChange($event)"
 *   (goBackRequested)="onGoBackRequested()"
 *   (setFormReadonly)="onSetFormReadonly($event)"
 *   (populateForm)="onPopulateForm($event)"
 *   (populateDefaultFields)="onPopulateDefaultFields($event)"
 *   (setViewMode)="onSetViewMode($event)">
 * </app-form-actions>
 */

import { Component, Input, Output, EventEmitter, inject } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { HttpClient } from '@angular/common/http';
import { CommonModule } from '@angular/common';
import { environment } from '../../../../environments/environment';
import { TableUtilsService } from '../../../services/table-utils.service';

// Angular Material imports for UI components
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';

/**
 * Form Actions Component
 * 
 * Provides comprehensive form action handling with the following capabilities:
 * - Form submission with data transformation
 * - Server-side validation
 * - Authorization workflow (approve/reject)
 * - Record deletion
 * - Error handling and user feedback
 * - Loading state management
 * - Success popup management
 * 
 * DATA TRANSFORMATION:
 * - Handles complex form data structures
 * - Transforms nested objects and arrays
 * - Manages multi-field data
 * - Filters out empty/null values
 * 
 * ERROR HANDLING:
 * - Comprehensive error message handling
 * - User-friendly error display
 * - API error response parsing
 * - Validation error management
 * 
 * STATE MANAGEMENT:
 * - Loading state tracking
 * - Success/error message management
 * - Form readonly state control
 * - View mode management
 */
@Component({
  selector: 'app-form-actions',
  standalone: true,
  imports: [
    CommonModule,
    MatButtonModule,
    MatIconModule
  ],
  templateUrl: './form-actions.component.html',
  styleUrl: './form-actions.component.scss'
})
export class FormActionsComponent {
  
  // ==================== CORE INPUTS ====================
  
  /** Angular FormGroup for reactive form integration */
  @Input() form!: FormGroup;
  
  /** Name of the database table for API operations */
  @Input() tableName!: string;
  
  /** Name of the screen/form for API operations */
  @Input() screenName!: string;
  
  /** Whether the table is tenant-based */
  @Input() isTenantBasedFlag: boolean = false;
  
  /** Authorization level number for workflow */
  @Input() authorizeNumber: number = 1;
  
  /** Whether the form is in view mode (read-only) */
  @Input() isViewMode: boolean = false;
  
  /** Whether to show success popup */
  @Input() showSuccessPopup: boolean = false;
  
  /** Success message to display */
  @Input() successMessage: string = '';
  
  /** Error message to display */
  @Input() errorMessage: string = '';
  
  /** Whether an operation is currently loading */
  @Input() isLoading: boolean = false;
  
  /** Validation result from server */
  @Input() validationResult: any;
  
  /** Array of field definitions for data transformation */
  @Input() fields: any[] = [];

  // ==================== OUTPUT EVENTS ====================
  
  /** Emitted when form submission is successful */
  @Output() submissionSuccess = new EventEmitter<boolean>();
  
  /** Emitted when error message changes */
  @Output() errorMessageChange = new EventEmitter<string>();
  
  /** Emitted when loading state changes */
  @Output() isLoadingChange = new EventEmitter<boolean>();
  
  /** Emitted when success popup visibility changes */
  @Output() showSuccessPopupChange = new EventEmitter<boolean>();
  
  /** Emitted when success message changes */
  @Output() successMessageChange = new EventEmitter<string>();
  
  /** Emitted when validation result changes */
  @Output() validationResultChange = new EventEmitter<any>();
  
  /** Emitted when user wants to go back */
  @Output() goBackRequested = new EventEmitter<void>();
  
  /** Emitted when form readonly state should change */
  @Output() setFormReadonly = new EventEmitter<boolean>();
  
  /** Emitted when form should be populated with data */
  @Output() populateForm = new EventEmitter<any>();
  
  /** Emitted when default fields should be populated */
  @Output() populateDefaultFields = new EventEmitter<any[]>();
  
  /** Emitted when view mode should change */
  @Output() setViewMode = new EventEmitter<boolean>();

  // ==================== DEPENDENCY INJECTION ====================
  
  /** HTTP client for API operations */
  private http = inject(HttpClient);
  
  /** Service for table utility operations */
  private tableUtilsService = inject(TableUtilsService);

  // ==================== AUTHORIZATION OPERATIONS ====================
  
  /**
   * Authorizes a record in the authorization workflow
   * Sends PUT request to authorize endpoint with proper parameters
   * Handles success/error states and user feedback
   */
  authorizeRecord() {
    this.errorMessageChange.emit("");
    this.isLoadingChange.emit(true);

    const id = this.form.get("ID")?.value;
    const tableNameToUse = this.tableName || this.screenName;
    const tablesApiId = this.tableUtilsService.extractTablesApiId(tableNameToUse);
    const apiUrl = `${environment.baseURL}/api/tables/${tablesApiId}/records/${id}/authorize`;
    const params = {
      isTenantBased: this.isTenantBasedFlag.toString(),
      authorizeNumber: this.authorizeNumber
    };
    
    this.http.put(apiUrl, {}, { withCredentials: true, params }).subscribe({
      next: (response: any) => {
        if (response && response.status === "success") {
          this.showSuccessPopupChange.emit(true);
          this.successMessageChange.emit("Record authorized successfully!");
          this.setViewMode.emit(false);
          this.setFormReadonly.emit(false);
          this.goBackRequested.emit();
          setTimeout(() => {
            this.showSuccessPopupChange.emit(false);
          }, 20000);
        } else {
          this.errorMessageChange.emit(response.message || "Authorization failed");
        }
      },
      error: (error) => {
        this.errorMessageChange.emit("An error occurred during authorization");
      },
      complete: () => this.isLoadingChange.emit(false)
    });
  }

  // ==================== FORM SUBMISSION ====================
  
  /**
   * Submits the form data to the server
   * Transforms form data and sends POST request
   * Handles success/error states and user feedback
   */
  onSubmit() {
    this.errorMessageChange.emit("");

    if (this.form.valid) {
      const tableNameToUse = this.tableName || this.screenName;
      const tablesApiId = this.tableUtilsService.extractTablesApiId(tableNameToUse);
      const apiUrl = `${environment.baseURL}/api/tables/${tablesApiId}/records`;
      const formData = this.buildFormData(this.form.getRawValue());
      const params = {
        isTenantBased: this.isTenantBasedFlag.toString(),
        authorizeNumber: this.authorizeNumber
      };
      
      this.http.post(apiUrl, formData, { withCredentials: true, params }).subscribe({
        next: (response: any) => {
          if (response.status === "success") {
            this.showSuccessPopupChange.emit(true);
            this.successMessageChange.emit("Record submitted successfully!");
            this.goBackRequested.emit();
            setTimeout(() => {
              this.showSuccessPopupChange.emit(false);
            }, 20000);
          } else if (response.status === "error") {
            this.errorMessageChange.emit(response.message[0].error || "An error occurred while submitting the form");
          }
        },
        error: (error: any) => {
          if (error.error && error.error.message) {
            this.errorMessageChange.emit(error.error.message[0].error);
          } else {
            this.errorMessageChange.emit("An unexpected error occurred while submitting the form");
          }
        }
      });
    }
  }

  // ==================== FORM VALIDATION ====================
  
  /**
   * Validates form data on the server
   * Sends validation request and handles response
   * Populates form with validation results and default fields
   */
  validateRecord() {
    this.errorMessageChange.emit("");
    this.isLoadingChange.emit(true);

    if (this.form.valid) {
      const tableNameToUse = this.tableName || this.screenName;
      const tablesApiId = this.tableUtilsService.extractTablesApiId(tableNameToUse);
      const apiUrl = `${environment.baseURL}/api/tables/${tablesApiId}/validate`;
      const formData = this.buildFormData(this.form.getRawValue());
      const params = {
        isTenantBased: this.isTenantBasedFlag.toString(),
        authorizeNumber: this.authorizeNumber
      };
      
      this.http.post(apiUrl, formData, { withCredentials: true, params }).subscribe({
        next: (response: any) => {
          if (response.status === "success") {
            this.validationResultChange.emit(response.data);
            this.populateForm.emit(response.data);
            // Handle defaultFields even in error response
            if (response.defaultFields && Array.isArray(response.defaultFields)) {
              this.populateDefaultFields.emit(response.defaultFields);
            }
          } else if (response.status === "error") {
            this.validationResultChange.emit(response.data);
            this.errorMessageChange.emit(response.message[0].error || "An error occurred during validation");
            this.populateForm.emit(response.data);
            // Handle defaultFields if present in validation response
            if (response.defaultFields && Array.isArray(response.defaultFields)) {
              this.populateDefaultFields.emit(response.defaultFields);
            }
          }
        },
        error: (error: any) => {
          this.validationResultChange.emit(error.error?.data || null);
          this.errorMessageChange.emit(error.error?.message[0].error || "An unexpected error occurred during validation");

          if (error.error?.data) {
            this.populateForm.emit(error.error.data);
          }
        },
        complete: () => this.isLoadingChange.emit(false)
      });
    }
  }

  // ==================== SUCCESS POPUP MANAGEMENT ====================
  
  /**
   * Closes the success popup
   * Emits event to hide success popup
   */
  closeSuccessPopup() {
    this.showSuccessPopupChange.emit(false);
  }

  // ==================== RECORD REJECTION ====================
  
  /**
   * Rejects a record in the authorization workflow
   * Sends PUT request to reject endpoint with proper parameters
   * Handles success/error states and user feedback
   */
  onRejectRecord() {
    this.errorMessageChange.emit("");
    this.isLoadingChange.emit(true);

    const id = this.form.get("ID")?.value;
    const tableNameToUse = this.tableName || this.screenName;
    const tablesApiId = this.tableUtilsService.extractTablesApiId(tableNameToUse);
    const apiUrl = `${environment.baseURL}/api/tables/${tablesApiId}/records/${id}/reject`;

    const params = {
      isTenantBased: this.isTenantBasedFlag.toString(),
      authorizeNumber: this.authorizeNumber
    };

    this.http.put(apiUrl, {}, {withCredentials: true, params}).subscribe({
      next: (response: any) => {
        if (response && response.status === "success") {
          this.showSuccessPopupChange.emit(true);
          this.successMessageChange.emit("Record rejected successfully!");
          this.setViewMode.emit(false);
          this.setFormReadonly.emit(false);
          this.goBackRequested.emit();
          setTimeout(() => {
            this.showSuccessPopupChange.emit(false);
          }, 20000);
        } else {
          this.errorMessageChange.emit(response.message || "Rejection failed");
        }
      },
      error: () => {
        this.errorMessageChange.emit("An error occurred during rejection");
      },
      complete: () => this.isLoadingChange.emit(false)
    });
  }

  // ==================== RECORD DELETION ====================
  
  /**
   * Deletes a record from the database
   * Sends DELETE request to remove the record
   * Handles success/error states and user feedback
   */
  onDeleteRecord() {
    this.errorMessageChange.emit("");
    this.isLoadingChange.emit(true);

    const id = this.form.get("ID")?.value;
    const tableNameToUse = this.tableName || this.screenName;
    const tablesApiId = this.tableUtilsService.extractTablesApiId(tableNameToUse);
    const apiUrl = `${environment.baseURL}/api/tables/${tablesApiId}/records/${id}`;

    const params = {
      isTenantBased: this.isTenantBasedFlag.toString(),
      authorizeNumber: this.authorizeNumber
    };

    this.http.delete(apiUrl, { withCredentials: true, params }).subscribe({
      next: (response: any) => {
        if (response && response.status === "success") {
          this.showSuccessPopupChange.emit(true);
          this.successMessageChange.emit("Record deleted successfully!");
          this.setViewMode.emit(false);
          this.setFormReadonly.emit(false);
          this.goBackRequested.emit();
          setTimeout(() => {
            this.showSuccessPopupChange.emit(false);
          }, 20000);
        } else {
          this.errorMessageChange.emit(response.message || "Deletion failed");
        }
      },
      error: (error) => {
        this.errorMessageChange.emit("An error occurred during deletion");
      },
      complete: () => this.isLoadingChange.emit(false)
    });
  }

  // ==================== DATA TRANSFORMATION ====================
  
  /**
   * Builds form data for API submission
   * Transforms complex form structures into API-compatible format
   * Handles nested objects, arrays, and multi-field data
   * Filters out empty/null values
   * 
   * @param data - Raw form data to transform
   * @returns Transformed data object for API submission
   */
  private buildFormData(data: any): any {
    const result: { [key: string]: any } = {};
    
    for (const key in data) {
      if (data.hasOwnProperty(key)) {
        const value = data[key];
        
        // Skip empty/null values
        if (value === null || value === undefined || value === "") continue;

        if (Array.isArray(value)) {
          // Handle array values (multi-fields and nested data)
          const field = this.fields.find(field => field.fieldName === key);
          if (field && field.isMulti) {
            // Handle multi-field arrays
            if (typeof value[0] === 'object' && value[0][field.fieldName] !== undefined) {
              result[key] = value.map((item: any) => item[field.fieldName]);
            } else {
              result[key] = value;
            }
          } else {
            // Handle nested object arrays
            const nestedData = value.map((item: any) => {
              if (item.fieldsToAppear) {
                return item.fieldsToAppear;
              } else {
                return this.buildFormData(item);
              }
            });
            if (nestedData.length > 0) {
              result[key] = nestedData;
            }
          }
        } else if (typeof value === "object") {
          // Handle nested objects
          const nestedObject = this.buildFormData(value);
          if (Object.keys(nestedObject).length > 0) {
            result[key] = nestedObject;
          }
        } else {
          // Handle simple values
          result[key] = value;
        }
      }
    }
    
    return result;
  }

  // Note: extractTablesApiId moved to TableUtilsService for reusability
}
